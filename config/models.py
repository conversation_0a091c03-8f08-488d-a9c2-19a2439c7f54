"""
Data models for the Deep Research Tool configuration.
This module defines the data structures used throughout the application.
Implemented according to development specification v3.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from enum import Enum
from datetime import datetime


class SearchProvider(Enum):
    """Available search providers."""
    GOOGLE = "google"
    BING = "bing"
    DUCKDUCKGO = "duckduckgo"
    WIKIPEDIA = "wikipedia"
    TAVILY = "tavily"


class AIProvider(Enum):
    """Available AI providers."""
    OPENAI = "openai"
    CLAUDE = "claude"
    GEMINI = "gemini"
    SILICONFLOW = "siliconflow"
    LOCAL = "local"


class ResearchPhase(Enum):
    """Research phases according to v3 specification."""
    TOPIC_INPUT = "topic_input"
    THINKING = "thinking"
    PLANNING = "planning"
    COLLECTION = "collection"
    ANALYSIS = "analysis"
    REPORT = "report"


class PhaseResult(BaseModel):
    """Result data for each research phase."""
    phase: ResearchPhase
    content: str
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ResearchSession(BaseModel):
    """Research session data model according to v3 specification."""
    session_id: str
    current_phase: ResearchPhase = ResearchPhase.TOPIC_INPUT
    completed_phases: List[ResearchPhase] = Field(default_factory=list)
    phase_results: Dict[str, PhaseResult] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

    def add_phase_result(self, phase: ResearchPhase, content: str, metadata: Dict[str, Any] = None):
        """Add a phase result and mark phase as completed."""
        if metadata is None:
            metadata = {}
        
        phase_result = PhaseResult(
            phase=phase,
            content=content,
            metadata=metadata
        )
        
        self.phase_results[phase.value] = phase_result
        if phase not in self.completed_phases:
            self.completed_phases.append(phase)
        self.updated_at = datetime.now()

    def get_phase_result(self, phase: ResearchPhase) -> Optional[PhaseResult]:
        """Get result for a specific phase."""
        return self.phase_results.get(phase.value)

    def is_phase_completed(self, phase: ResearchPhase) -> bool:
        """Check if a phase is completed."""
        return phase in self.completed_phases


class SearchResult(BaseModel):
    """Search result data model."""
    title: str
    url: str
    snippet: str
    source: str
    relevance_score: Optional[float] = None
    timestamp: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class MaterialItem(BaseModel):
    """Research material item according to v3 specification."""
    title: str
    url: Optional[str] = None
    content: str
    source: str = Field(..., description="Source type: 'web', 'local', or 'uploaded'")
    relevance: float = Field(default=0.0, ge=0.0, le=1.0)
    created_at: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = Field(default_factory=dict)

    @validator('source')
    def validate_source(cls, v):
        valid_sources = ['web', 'local', 'uploaded']
        if v not in valid_sources:
            raise ValueError(f"Source must be one of: {valid_sources}")
        return v

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AIConfig(BaseModel):
    """AI service configuration according to v3 specification."""
    provider: AIProvider = AIProvider.SILICONFLOW
    api_key: str
    base_url: str = "https://api.siliconflow.cn/v1"
    model: str = "Qwen/Qwen3-8B"
    timeout: int = Field(default=30, gt=0)
    max_tokens: int = Field(default=2000, gt=0)
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)

    @validator('api_key')
    def validate_api_key(cls, v):
        if not v or not v.strip():
            raise ValueError("AI API key cannot be empty")
        return v.strip()


class SearchConfig(BaseModel):
    """Search engine configuration according to v3 specification."""
    provider: str = "tavily"
    providers: List[SearchProvider] = Field(default_factory=lambda: [SearchProvider.TAVILY])
    api_key: str
    max_results: int = Field(default=10, gt=0, le=50)
    timeout: int = Field(default=20, gt=0)

    @validator('api_key')
    def validate_api_key(cls, v):
        if not v or not v.strip():
            raise ValueError("Search API key cannot be empty")
        return v.strip()


class AppConfig(BaseModel):
    """Application configuration according to v3 specification."""
    title: str = "深度研究工具"
    port: int = Field(default=7860, gt=0, le=65535)
    auto_advance: bool = True
    session_timeout: int = Field(default=3600, gt=0)  # seconds
    max_file_size: int = Field(default=10 * 1024 * 1024, gt=0)  # 10MB in bytes

    @validator('title')
    def validate_title(cls, v):
        if not v or not v.strip():
            raise ValueError("Application title cannot be empty")
        return v.strip()


class StorageConfig(BaseModel):
    """Storage configuration for sessions and uploads."""
    sessions_dir: str = "./storage/sessions"
    uploads_dir: str = "./storage/uploads"
    temp_dir: str = "./storage/temp"
    max_file_size: int = Field(default=10 * 1024 * 1024, gt=0)  # 10MB
    allowed_extensions: List[str] = Field(
        default_factory=lambda: [".pdf", ".txt", ".md", ".docx"]
    )
    session_timeout: int = Field(default=3600, gt=0)  # seconds


class ServerConfig(BaseModel):
    """Server configuration according to v3 specification."""
    host: str = "0.0.0.0"
    port: int = Field(default=3366, gt=0, le=65535)
    debug: bool = False
    share: bool = True
    auth: Optional[tuple] = None
    ssl_keyfile: Optional[str] = None
    ssl_certfile: Optional[str] = None
    max_threads: int = Field(default=40, gt=0)


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = Field(default=10 * 1024 * 1024, gt=0)  # 10MB
    backup_count: int = Field(default=5, gt=0)


class SecurityConfig(BaseModel):
    """Security configuration."""
    enable_auth: bool = False
    username: Optional[str] = None
    password: Optional[str] = None
    secret_key: Optional[str] = None
    session_timeout: int = Field(default=3600, gt=0)  # seconds


class CacheConfig(BaseModel):
    """Cache configuration."""
    enable_cache: bool = True
    cache_dir: str = "./storage/cache"
    max_cache_size: int = Field(default=100 * 1024 * 1024, gt=0)  # 100MB
    cache_ttl: int = Field(default=3600, gt=0)  # seconds


class FeatureFlags(BaseModel):
    """Feature flags configuration."""
    enable_search: bool = True
    enable_analysis: bool = True
    enable_file_upload: bool = True
    enable_session_management: bool = True


class RetrieverConfig(BaseModel):
    """Content retriever configuration."""
    base_url: str = "https://r.jina.ai"
    timeout: int = Field(default=20, gt=0)


class CompleteConfig(BaseModel):
    """Complete application configuration combining all sub-configurations."""
    ai: AIConfig
    search: SearchConfig
    server: ServerConfig = Field(default_factory=ServerConfig)
    app: AppConfig = Field(default_factory=AppConfig)
    storage: StorageConfig = Field(default_factory=StorageConfig)
    retriever: RetrieverConfig = Field(default_factory=RetrieverConfig)
    features: FeatureFlags = Field(default_factory=FeatureFlags)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    cache: CacheConfig = Field(default_factory=CacheConfig)

    def validate_required_configs(self) -> List[str]:
        """Validate that all required configurations are present and valid."""
        errors = []
        
        try:
            # Validate AI config
            if not self.ai.api_key:
                errors.append("AI API key is required")
        except Exception as e:
            errors.append(f"AI configuration error: {e}")
        
        try:
            # Validate search config
            if not self.search.api_key:
                errors.append("Search API key is required")
        except Exception as e:
            errors.append(f"Search configuration error: {e}")
        
        return errors


# Legacy compatibility models for backward compatibility


# Legacy dataclass models for backward compatibility (simplified)
from dataclasses import dataclass


@dataclass
class LegacyServerConfig:
    """Legacy server configuration settings."""
    host: str = "0.0.0.0"
    port: int = 3366
    debug: bool = False
    share: bool = True
    auth: Optional[tuple] = None
    ssl_keyfile: Optional[str] = None
    ssl_certfile: Optional[str] = None
    max_threads: int = 40


@dataclass
class LegacySearchResult:
    """Legacy search result data model."""
    title: str
    url: str
    snippet: str
    source: str
    relevance_score: Optional[float] = None
    timestamp: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}