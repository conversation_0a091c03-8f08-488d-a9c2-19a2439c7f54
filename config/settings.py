"""
Application settings and configuration management.
This module handles loading and managing application configuration.
Implemented according to development specification v3.
"""

import os
import yaml
import re
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from pydantic import ValidationError

from .models import (
    CompleteConfig, AIConfig, SearchConfig, AppConfig,
    StorageConfig, RetrieverConfig, ServerConfig, FeatureFlags,
    LoggingConfig, SecurityConfig, CacheConfig
)


class ConfigurationError(Exception):
    """Configuration related errors."""
    def __init__(self, message: str, config_path: Optional[str] = None):
        self.config_path = config_path
        super().__init__(f"Configuration error: {message}")


class ConfigManager:
    """Manages application configuration from YAML files and environment variables."""
    
    def __init__(self, config_path: str = "config.yml"):
        """
        Initialize configuration manager.
        
        Args:
            config_path: Path to YAML configuration file
        """
        self.config_path = Path(config_path)
        self.config: Optional[CompleteConfig] = None
        self._env_var_pattern = re.compile(r'\$\{([^}]+)\}')
    
    def load_config(self) -> CompleteConfig:
        """Load configuration from file and environment variables."""
        if self.config is None:
            self.config = self._load_from_sources()
        return self.config
    
    def reload_config(self) -> CompleteConfig:
        """Reload configuration from sources."""
        self.config = None
        return self.load_config()
    
    def _load_from_sources(self) -> CompleteConfig:
        """Load configuration from YAML file and environment variables."""
        try:
            # Load base configuration from YAML
            config_data = self._load_yaml_config()
            
            # Replace environment variables
            config_data = self._replace_env_variables(config_data)
            
            # Build and validate configuration objects
            return self._build_config(config_data)
        
        except Exception as e:
            raise ConfigurationError(
                f"Failed to load configuration: {e}",
                str(self.config_path)
            )
    
    def _load_yaml_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        if not self.config_path.exists():
            # Return default configuration structure if no file exists
            return {
                'ai': {
                    'provider': 'siliconflow',
                    'api_key': '${API_KEY}',
                    'base_url': 'https://api.siliconflow.cn/v1',
                    'model': 'Qwen/Qwen3-8B'
                },
                'search': {
                    'provider': 'tavily',
                    'api_key': '${TAVILY_API_KEY}',
                    'max_results': 10
                }
            }
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if not content.strip():
                    return {}
                return yaml.safe_load(content) or {}
        except yaml.YAMLError as e:
            raise ConfigurationError(
                f"Invalid YAML in configuration file: {e}",
                str(self.config_path)
            )
        except Exception as e:
            raise ConfigurationError(
                f"Unable to read configuration file: {e}",
                str(self.config_path)
            )
    
    def _replace_env_variables(self, obj: Any) -> Any:
        """Recursively replace environment variables in configuration objects."""
        if isinstance(obj, dict):
            return {k: self._replace_env_variables(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._replace_env_variables(item) for item in obj]
        elif isinstance(obj, str):
            return self._substitute_env_vars(obj)
        else:
            return obj
    
    def _substitute_env_vars(self, value: str) -> str:
        """Replace environment variable placeholders in string values."""
        def replace_var(match):
            var_name = match.group(1)
            # Support default values: ${VAR:default_value}
            if ':' in var_name:
                var_name, default_value = var_name.split(':', 1)
            else:
                default_value = None
            
            env_value = os.getenv(var_name)
            if env_value is not None:
                return env_value
            elif default_value is not None:
                return default_value
            else:
                # Keep placeholder if no environment variable or default
                return match.group(0)
        
        return self._env_var_pattern.sub(replace_var, value)
    
    def _build_config(self, config_data: Dict[str, Any]) -> CompleteConfig:
        """Build CompleteConfig object from configuration data."""
        try:
            # Extract configuration sections with defaults
            ai_data = config_data.get('ai', {})
            search_data = config_data.get('search', {})
            server_data = config_data.get('server', {})
            app_data = config_data.get('app', {})
            storage_data = config_data.get('storage', {})
            retriever_data = config_data.get('retriever', {})
            features_data = config_data.get('features', {})
            logging_data = config_data.get('logging', {})
            security_data = config_data.get('security', {})
            cache_data = config_data.get('cache', {})

            # Build individual config objects
            ai_config = AIConfig(**ai_data)
            search_config = SearchConfig(**search_data)
            server_config = ServerConfig(**server_data)
            app_config = AppConfig(**app_data)
            storage_config = StorageConfig(**storage_data)
            retriever_config = RetrieverConfig(**retriever_data)
            features_config = FeatureFlags(**features_data)
            logging_config = LoggingConfig(**logging_data)
            security_config = SecurityConfig(**security_data)
            cache_config = CacheConfig(**cache_data)

            # Create complete configuration
            complete_config = CompleteConfig(
                ai=ai_config,
                search=search_config,
                server=server_config,
                app=app_config,
                storage=storage_config,
                retriever=retriever_config,
                features=features_config,
                logging=logging_config,
                security=security_config,
                cache=cache_config
            )

            return complete_config
            
        except ValidationError as e:
            error_details = []
            for error in e.errors():
                field = " -> ".join(str(x) for x in error['loc'])
                message = error['msg']
                error_details.append(f"{field}: {message}")
            
            raise ConfigurationError(
                f"Configuration validation failed:\n" + "\n".join(error_details)
            )
        except Exception as e:
            raise ConfigurationError(f"Failed to build configuration: {e}")
    
    def validate_config(self) -> List[str]:
        """
        Validate the current configuration and return list of validation errors.
        
        Returns:
            List of validation error messages, empty if valid
        """
        try:
            config = self.load_config()
            errors = []
            
            # Validate using built-in validation
            config_errors = config.validate_required_configs()
            errors.extend(config_errors)
            
            # Check storage directories
            storage_errors = self._validate_storage_config(config.storage)
            errors.extend(storage_errors)
            
            # Check environment variables are resolved
            env_errors = self._check_unresolved_env_vars(config)
            errors.extend(env_errors)
            
            return errors
            
        except Exception as e:
            return [f"Configuration loading failed: {e}"]
    
    def _validate_storage_config(self, storage: StorageConfig) -> List[str]:
        """Validate storage configuration and directory accessibility."""
        errors = []
        
        directories = [
            ('sessions_dir', storage.sessions_dir),
            ('uploads_dir', storage.uploads_dir),
            ('temp_dir', storage.temp_dir)
        ]
        
        for dir_name, dir_path in directories:
            path_obj = Path(dir_path)
            try:
                # Try to create directory if it doesn't exist
                path_obj.mkdir(parents=True, exist_ok=True)
                
                # Check if directory is writable
                test_file = path_obj / '.write_test'
                test_file.write_text('test')
                test_file.unlink()
                
            except Exception as e:
                errors.append(f"Storage directory '{dir_name}' ({dir_path}) is not accessible: {e}")
        
        return errors
    
    def _check_unresolved_env_vars(self, config: CompleteConfig) -> List[str]:
        """Check for unresolved environment variable placeholders."""
        errors = []
        
        # Convert config to dict and check for ${} patterns
        config_dict = config.dict()
        unresolved = self._find_unresolved_vars(config_dict)
        
        for var_path in unresolved:
            errors.append(f"Unresolved environment variable: {var_path}")
        
        return errors
    
    def _find_unresolved_vars(self, obj: Any, path: str = "") -> List[str]:
        """Recursively find unresolved environment variable placeholders."""
        unresolved = []
        
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                unresolved.extend(self._find_unresolved_vars(value, current_path))
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                current_path = f"{path}[{i}]"
                unresolved.extend(self._find_unresolved_vars(item, current_path))
        elif isinstance(obj, str) and self._env_var_pattern.search(obj):
            unresolved.append(path)
        
        return unresolved
    
    def get_config(self) -> CompleteConfig:
        """Get the current configuration."""
        return self.load_config()
    
    def create_sample_config(self, output_path: str = "config.yml") -> None:
        """Create a sample configuration file with environment variable placeholders."""
        sample_config = {
            'ai': {
                'provider': 'siliconflow',
                'api_key': '${API_KEY}',
                'base_url': 'https://api.siliconflow.cn/v1',
                'model': 'Qwen/Qwen3-8B',
                'timeout': 30,
                'max_tokens': 2000,
                'temperature': 0.7
            },
            'search': {
                'provider': 'tavily',
                'api_key': '${TAVILY_API_KEY}',
                'max_results': 10,
                'timeout': 20
            },
            'app': {
                'title': '深度研究工具',
                'port': 7860,
                'auto_advance': True,
                'session_timeout': 3600,
                'max_file_size': 10485760
            },
            'storage': {
                'sessions_dir': './storage/sessions',
                'uploads_dir': './storage/uploads',
                'temp_dir': './storage/temp',
                'max_file_size': 10485760,
                'allowed_extensions': ['.pdf', '.txt', '.md', '.docx'],
                'session_timeout': 3600
            },
            'retriever': {
                'base_url': 'https://r.jina.ai',
                'timeout': 20
            }
        }
        
        output_path_obj = Path(output_path)
        with open(output_path_obj, 'w', encoding='utf-8') as f:
            yaml.dump(sample_config, f, default_flow_style=False, allow_unicode=True, indent=2)
        
        print(f"Sample configuration created at: {output_path}")
    
    def get_env_vars_info(self) -> Dict[str, Dict[str, str]]:
        """Get information about required environment variables."""
        return {
            'required': {
                'API_KEY': 'AI service API key (e.g., SiliconFlow API key)',
                'TAVILY_API_KEY': 'Tavily search API key'
            },
            'optional': {
                'AI_MODEL': 'AI model name (default: Qwen/Qwen3-8B)',
                'AI_BASE_URL': 'AI service base URL (default: https://api.siliconflow.cn/v1)',
                'SEARCH_MAX_RESULTS': 'Maximum search results (default: 10)',
                'APP_PORT': 'Application port (default: 7860)',
                'SESSION_TIMEOUT': 'Session timeout in seconds (default: 3600)'
            }
        }


# Global configuration manager instance
config_manager = ConfigManager()


def get_config() -> CompleteConfig:
    """Get the application configuration."""
    return config_manager.get_config()


def reload_config() -> CompleteConfig:
    """Reload the application configuration."""
    return config_manager.reload_config()


def validate_config() -> List[str]:
    """Validate the application configuration."""
    return config_manager.validate_config()


def create_sample_config(output_path: str = "config.yml") -> None:
    """Create a sample configuration file."""
    config_manager.create_sample_config(output_path)


def get_env_vars_info() -> Dict[str, Dict[str, str]]:
    """Get information about environment variables."""
    return config_manager.get_env_vars_info()


# Utility functions for environment validation
def validate_environment() -> List[str]:
    """Validate environment variables, return list of errors."""
    errors = []
    required_vars = ["API_KEY", "TAVILY_API_KEY"]
    
    for var in required_vars:
        if not os.getenv(var):
            errors.append(f"Missing required environment variable: {var}")
    
    return errors


def load_config_from_env() -> Dict[str, Any]:
    """Load configuration from environment variables (legacy compatibility)."""
    return {
        "ai": {
            "api_key": os.getenv("API_KEY"),
            "model": os.getenv("AI_MODEL", "Qwen/Qwen3-8B"),
            "base_url": os.getenv("AI_BASE_URL", "https://api.siliconflow.cn/v1")
        },
        "search": {
            "api_key": os.getenv("TAVILY_API_KEY"),
            "max_results": int(os.getenv("SEARCH_MAX_RESULTS", "10"))
        },
        "app": {
            "auto_advance": os.getenv("AUTO_ADVANCE", "true").lower() == "true",
            "session_timeout": int(os.getenv("SESSION_TIMEOUT", "3600")),
            "port": int(os.getenv("APP_PORT", "7860"))
        }
    }