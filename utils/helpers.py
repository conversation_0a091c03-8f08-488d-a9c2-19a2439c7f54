"""
Helper utilities for Deep Research Tool.
Simple utility functions according to development specification v3.
"""

import re
import uuid
import hashlib
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
from pathlib import Path
from urllib.parse import urlparse
import json


def generate_session_id() -> str:
    """生成唯一的会话ID"""
    return f"session_{uuid.uuid4().hex[:16]}"


def generate_file_id() -> str:
    """生成唯一的文件ID"""
    return f"file_{uuid.uuid4().hex[:12]}"


def generate_unique_id() -> str:
    """生成唯一ID"""
    return uuid.uuid4().hex


def clean_text(text: str) -> str:
    """清理和标准化文本内容"""
    if not text:
        return ""
    
    # 移除多余空格
    text = re.sub(r'\s+', ' ', text)
    
    # 移除多余换行
    text = re.sub(r'\n\s*\n', '\n\n', text)
    
    # 清理前后空格
    return text.strip()


def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
    """截断文本到指定长度"""
    if not text or len(text) <= max_length:
        return text
    
    if max_length <= len(suffix):
        return text[:max_length]
    
    truncated = text[:max_length - len(suffix)]
    
    # 尝试在词边界处截断
    last_space = truncated.rfind(' ')
    if last_space > max_length * 0.8:  # 如果可以在80%长度处截断
        truncated = truncated[:last_space]
    
    return truncated + suffix


def get_file_extension(filename: str) -> str:
    """获取文件扩展名"""
    return Path(filename).suffix.lower()


def extract_domain(url: str) -> str:
    """从URL中提取域名"""
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        # 移除www前缀
        if domain.startswith('www.'):
            domain = domain[4:]
        return domain
    except Exception:
        return ""


def is_valid_url(url: str) -> bool:
    """验证URL格式"""
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def validate_url(url: str) -> bool:
    """验证URL格式（别名）"""
    return is_valid_url(url)


def normalize_query(query: str) -> str:
    """标准化查询字符串"""
    if not query:
        return ""
    return clean_text(query).lower()


def sanitize_filename(filename: str) -> str:
    """清理文件名，使其安全存储"""
    if not filename:
        return "untitled"
    
    # 移除或替换非法字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # 移除前后的点和空格
    filename = filename.strip('. ')
    
    # 限制长度
    if len(filename) > 255:
        name, ext = Path(filename).stem, Path(filename).suffix
        max_name_length = 255 - len(ext)
        filename = name[:max_name_length] + ext
    
    return filename or "untitled"


def calculate_text_similarity(text1: str, text2: str) -> float:
    """计算两个文本的相似度（简化版）"""
    if not text1 or not text2:
        return 0.0

    # 简单的基于词汇重叠的相似度计算
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())

    if not words1 or not words2:
        return 0.0

    intersection = words1.intersection(words2)
    union = words1.union(words2)

    return len(intersection) / len(union) if union else 0.0


def format_datetime(dt: Union[datetime, str, None] = None) -> str:
    """格式化日期时间"""
    if dt is None:
        dt = datetime.now()

    if isinstance(dt, str):
        try:
            dt = datetime.fromisoformat(dt)
        except ValueError:
            return dt

    if isinstance(dt, datetime):
        return dt.strftime("%Y-%m-%d %H:%M:%S")

    return str(dt)


def parse_datetime(dt_str: str) -> Optional[datetime]:
    """解析日期时间字符串"""
    if not dt_str:
        return None

    try:
        return datetime.fromisoformat(dt_str)
    except ValueError:
        try:
            return datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            return None


def format_timestamp(dt: Union[datetime, str, None] = None) -> str:
    """格式化时间戳（别名）"""
    return format_datetime(dt)


def extract_text_content(html_content: str) -> str:
    """从HTML内容中提取纯文本（简化版）"""
    if not html_content:
        return ""
    
    # 移除HTML标签的简单方法
    text = re.sub(r'<[^>]+>', ' ', html_content)
    
    # 解码HTML实体
    html_entities = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#x27;': "'",
        '&#x2F;': '/',
        '&#39;': "'",
        '&nbsp;': ' '
    }
    
    for entity, char in html_entities.items():
        text = text.replace(entity, char)
    
    return clean_text(text)


def hash_text(text: str) -> str:
    """生成文本的SHA256哈希值"""
    if not text:
        return ""
    return hashlib.sha256(text.encode('utf-8')).hexdigest()


def safe_json_loads(json_string: str, default: Any = None) -> Any:
    """安全解析JSON字符串"""
    if not json_string:
        return default
    
    try:
        return json.loads(json_string)
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """安全序列化对象为JSON字符串"""
    try:
        return json.dumps(obj, ensure_ascii=False, default=str)
    except (TypeError, ValueError):
        return default


def deep_merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """深度合并两个字典"""
    result = dict1.copy()

    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value

    return result


def flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
    """扁平化嵌套字典"""
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


def chunk_text(text: str, chunk_size: int, overlap: int = 0) -> List[str]:
    """将文本分块"""
    if not text or chunk_size <= 0:
        return []

    chunks = []
    start = 0

    while start < len(text):
        end = start + chunk_size
        chunk = text[start:end]
        chunks.append(chunk)

        if end >= len(text):
            break

        start = end - overlap

    return chunks


def chunk_list(items: List[Any], chunk_size: int) -> List[List[Any]]:
    """将列表分块"""
    if not items or chunk_size <= 0:
        return []

    return [items[i:i + chunk_size] for i in range(0, len(items), chunk_size)]


def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    if not email:
        return False

    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def rate_limit_key(identifier: str, window: str = "hour") -> str:
    """生成速率限制键"""
    timestamp = datetime.now()

    if window == "minute":
        time_key = timestamp.strftime("%Y%m%d%H%M")
    elif window == "hour":
        time_key = timestamp.strftime("%Y%m%d%H")
    elif window == "day":
        time_key = timestamp.strftime("%Y%m%d")
    else:
        time_key = timestamp.strftime("%Y%m%d%H")

    return f"rate_limit:{identifier}:{time_key}"


def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """从文本中提取关键词（简化版）"""
    if not text:
        return []
    
    # 提取单词
    words = re.findall(r'\b\w{3,}\b', text.lower())
    
    # 简单的停用词
    stop_words = {
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
        'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
        'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
        'this', 'that', 'these', 'those'
    }
    
    # 过滤停用词
    keywords = [word for word in words if word not in stop_words]
    
    # 计算频率并返回最常见的
    from collections import Counter
    word_counts = Counter(keywords)
    
    return [word for word, count in word_counts.most_common(max_keywords)]


def ensure_directory(path: Union[str, Path]) -> Path:
    """确保目录存在，如果不存在则创建"""
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def get_file_size_human(size_bytes: int) -> str:
    """将字节大小转换为人类可读格式"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024
        i += 1
    
    return f"{round(size_bytes, 2)} {size_names[i]}"


class ProgressTracker:
    """进度跟踪器"""

    def __init__(self, total: int, description: str = "Processing"):
        self.total = total
        self.current = 0
        self.description = description
        self.start_time = datetime.now()

    def update(self, increment: int = 1):
        """更新进度"""
        self.current = min(self.current + increment, self.total)

    def get_progress(self) -> float:
        """获取进度百分比"""
        if self.total == 0:
            return 1.0
        return self.current / self.total

    def get_eta(self) -> Optional[datetime]:
        """估算完成时间"""
        if self.current == 0:
            return None

        elapsed = (datetime.now() - self.start_time).total_seconds()
        rate = self.current / elapsed

        if rate == 0:
            return None

        remaining_items = self.total - self.current
        remaining_seconds = remaining_items / rate

        return datetime.now() + timedelta(seconds=remaining_seconds)

    def is_complete(self) -> bool:
        """检查是否完成"""
        return self.current >= self.total


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        import time
                        time.sleep(delay * (2 ** attempt))  # 指数退避
                    continue

            raise last_exception
        return wrapper
    return decorator


class SimpleTimer:
    """简单的计时器"""

    def __init__(self):
        self.start_time = None
        self.end_time = None

    def start(self):
        """开始计时"""
        self.start_time = datetime.now()
        self.end_time = None

    def stop(self) -> float:
        """停止计时并返回耗时（秒）"""
        if self.start_time is None:
            return 0.0

        self.end_time = datetime.now()
        return (self.end_time - self.start_time).total_seconds()

    def elapsed(self) -> float:
        """返回当前耗时（秒）"""
        if self.start_time is None:
            return 0.0

        end_time = self.end_time or datetime.now()
        return (end_time - self.start_time).total_seconds()