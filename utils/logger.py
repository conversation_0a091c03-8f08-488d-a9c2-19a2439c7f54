"""
Logging utilities for Deep Research Tool.
Simple logging configuration according to development specification v3.
"""

import logging
import sys
import json
import time
import functools
import asyncio
from pathlib import Path
from typing import Optional, Dict, Any, Callable
from datetime import datetime
from contextlib import contextmanager


class JSONFormatter(logging.Formatter):
    """JSON格式的日志格式化器"""

    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }

        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)

        return json.dumps(log_entry, ensure_ascii=False)


class ColorFormatter(logging.Formatter):
    """带颜色的日志格式化器"""

    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
    }
    RESET = '\033[0m'

    def format(self, record):
        color = self.COLORS.get(record.levelname, '')
        record.levelname = f"{color}{record.levelname}{self.RESET}"
        return super().format(record)


class LoggerManager:
    """日志管理器"""

    def __init__(self):
        self.loggers: Dict[str, logging.Logger] = {}
        self.default_level = logging.INFO

    def get_logger(self, name: str) -> logging.Logger:
        """获取或创建日志器"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            logger.setLevel(self.default_level)
            self.loggers[name] = logger
        return self.loggers[name]

    def set_level(self, level: str):
        """设置所有日志器的级别"""
        log_level = getattr(logging, level.upper(), logging.INFO)
        self.default_level = log_level
        for logger in self.loggers.values():
            logger.setLevel(log_level)


# 全局日志管理器实例
_logger_manager = LoggerManager()


def get_logger_manager() -> LoggerManager:
    """获取日志管理器实例"""
    return _logger_manager


def setup_logging(level: str = "INFO", log_file: Optional[str] = None):
    """
    设置日志配置 - 简化版本
    
    Args:
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 可选的日志文件路径
    """
    # 设置日志级别
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(log_level)
    
    handlers = [console_handler]
    
    # 文件处理器（可选）
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(log_level)
        handlers.append(file_handler)
    
    # 配置根日志器
    logging.basicConfig(
        level=log_level,
        handlers=handlers,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 防止重复日志
    logging.getLogger().propagate = False


def get_logger(name: str = __name__) -> logging.Logger:
    """
    获取日志器实例

    Args:
        name: 日志器名称，通常使用__name__

    Returns:
        配置好的日志器实例
    """
    return _logger_manager.get_logger(name)


def set_log_level(level: str):
    """设置日志级别"""
    _logger_manager.set_level(level)


def log_function_call(func: Callable) -> Callable:
    """函数调用日志装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        logger.debug(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")

        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger.debug(f"{func.__name__} completed in {duration:.3f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"{func.__name__} failed after {duration:.3f}s: {e}")
            raise

    return wrapper


def log_async_function_call(func: Callable) -> Callable:
    """异步函数调用日志装饰器"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        logger.debug(f"Calling async {func.__name__} with args={args}, kwargs={kwargs}")

        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            logger.debug(f"Async {func.__name__} completed in {duration:.3f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Async {func.__name__} failed after {duration:.3f}s: {e}")
            raise

    return wrapper


class LogContext:
    """日志上下文管理器"""

    def __init__(self, logger: logging.Logger, context: Dict[str, Any]):
        self.logger = logger
        self.context = context
        self.old_context = getattr(logger, '_context', {})

    def __enter__(self):
        self.logger._context = {**self.old_context, **self.context}
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.logger._context = self.old_context


@contextmanager
def with_log_context(logger: logging.Logger, **context):
    """日志上下文管理器"""
    old_context = getattr(logger, '_context', {})
    logger._context = {**old_context, **context}
    try:
        yield
    finally:
        logger._context = old_context


class PerformanceLogger:
    """性能日志记录器"""

    def __init__(self, logger: logging.Logger, threshold: float = 1.0):
        self.logger = logger
        self.threshold = threshold
        self.start_time = None

    def start(self, operation: str):
        """开始记录操作"""
        self.operation = operation
        self.start_time = time.time()
        self.logger.debug(f"Starting {operation}")

    def end(self):
        """结束记录操作"""
        if self.start_time is None:
            return

        duration = time.time() - self.start_time
        if duration > self.threshold:
            self.logger.warning(f"Slow operation: {self.operation} took {duration:.3f}s")
        else:
            self.logger.debug(f"Completed {self.operation} in {duration:.3f}s")


def create_performance_logger(logger: logging.Logger, threshold: float = 1.0) -> PerformanceLogger:
    """创建性能日志记录器"""
    return PerformanceLogger(logger, threshold)


def log_slow_operations(threshold: float = 1.0):
    """慢操作日志装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(func.__module__)
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                if duration > threshold:
                    logger.warning(f"Slow operation: {func.__name__} took {duration:.3f}s")

                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{func.__name__} failed after {duration:.3f}s: {e}")
                raise

        return wrapper
    return decorator


def log_slow_async_operations(threshold: float = 1.0):
    """慢异步操作日志装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            logger = get_logger(func.__module__)
            start_time = time.time()

            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time

                if duration > threshold:
                    logger.warning(f"Slow async operation: {func.__name__} took {duration:.3f}s")

                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Async {func.__name__} failed after {duration:.3f}s: {e}")
                raise

        return wrapper
    return decorator


# 便捷的日志函数
def debug(message: str, logger_name: str = "deep_research"):
    """记录调试信息"""
    get_logger(logger_name).debug(message)


def info(message: str, logger_name: str = "deep_research"):
    """记录信息"""
    get_logger(logger_name).info(message)


def warning(message: str, logger_name: str = "deep_research"):
    """记录警告"""
    get_logger(logger_name).warning(message)


def error(message: str, logger_name: str = "deep_research"):
    """记录错误"""
    get_logger(logger_name).error(message)


def critical(message: str, logger_name: str = "deep_research"):
    """记录严重错误"""
    get_logger(logger_name).critical(message)


# 初始化默认日志配置
def _init_default_logging():
    """初始化默认日志配置"""
    try:
        setup_logging(
            level="INFO",
            log_file="logs/deep_research.log"
        )
    except Exception:
        # 如果文件日志失败，只使用控制台日志
        setup_logging(level="INFO")


# 在模块加载时初始化日志
_init_default_logging()