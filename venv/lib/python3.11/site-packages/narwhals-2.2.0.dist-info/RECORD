narwhals-2.2.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
narwhals-2.2.0.dist-info/METADATA,sha256=dZ3xOSftBj_ZW8k4oQFVUHeF60VaUE_WF7BA9-NSoxI,11312
narwhals-2.2.0.dist-info/RECORD,,
narwhals-2.2.0.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
narwhals-2.2.0.dist-info/licenses/LICENSE.md,sha256=heMD6hta6RzeBucppx59AUCgr_ukRY0ABj0bcrN3mKs,1071
narwhals/__init__.py,sha256=FGVSkIo-2xnlr-PAnUz9_lUsoAVLmwSko7vvKt1dhwM,3206
narwhals/__pycache__/__init__.cpython-311.pyc,,
narwhals/__pycache__/_constants.cpython-311.pyc,,
narwhals/__pycache__/_duration.cpython-311.pyc,,
narwhals/__pycache__/_enum.cpython-311.pyc,,
narwhals/__pycache__/_exceptions.cpython-311.pyc,,
narwhals/__pycache__/_expression_parsing.cpython-311.pyc,,
narwhals/__pycache__/_namespace.cpython-311.pyc,,
narwhals/__pycache__/_translate.cpython-311.pyc,,
narwhals/__pycache__/_typing.cpython-311.pyc,,
narwhals/__pycache__/_typing_compat.cpython-311.pyc,,
narwhals/__pycache__/_utils.cpython-311.pyc,,
narwhals/__pycache__/dataframe.cpython-311.pyc,,
narwhals/__pycache__/dependencies.cpython-311.pyc,,
narwhals/__pycache__/dtypes.cpython-311.pyc,,
narwhals/__pycache__/exceptions.cpython-311.pyc,,
narwhals/__pycache__/expr.cpython-311.pyc,,
narwhals/__pycache__/expr_cat.cpython-311.pyc,,
narwhals/__pycache__/expr_dt.cpython-311.pyc,,
narwhals/__pycache__/expr_list.cpython-311.pyc,,
narwhals/__pycache__/expr_name.cpython-311.pyc,,
narwhals/__pycache__/expr_str.cpython-311.pyc,,
narwhals/__pycache__/expr_struct.cpython-311.pyc,,
narwhals/__pycache__/functions.cpython-311.pyc,,
narwhals/__pycache__/group_by.cpython-311.pyc,,
narwhals/__pycache__/schema.cpython-311.pyc,,
narwhals/__pycache__/selectors.cpython-311.pyc,,
narwhals/__pycache__/series.cpython-311.pyc,,
narwhals/__pycache__/series_cat.cpython-311.pyc,,
narwhals/__pycache__/series_dt.cpython-311.pyc,,
narwhals/__pycache__/series_list.cpython-311.pyc,,
narwhals/__pycache__/series_str.cpython-311.pyc,,
narwhals/__pycache__/series_struct.cpython-311.pyc,,
narwhals/__pycache__/this.cpython-311.pyc,,
narwhals/__pycache__/translate.cpython-311.pyc,,
narwhals/__pycache__/typing.cpython-311.pyc,,
narwhals/__pycache__/utils.cpython-311.pyc,,
narwhals/_arrow/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_arrow/__pycache__/__init__.cpython-311.pyc,,
narwhals/_arrow/__pycache__/dataframe.cpython-311.pyc,,
narwhals/_arrow/__pycache__/expr.cpython-311.pyc,,
narwhals/_arrow/__pycache__/group_by.cpython-311.pyc,,
narwhals/_arrow/__pycache__/namespace.cpython-311.pyc,,
narwhals/_arrow/__pycache__/selectors.cpython-311.pyc,,
narwhals/_arrow/__pycache__/series.cpython-311.pyc,,
narwhals/_arrow/__pycache__/series_cat.cpython-311.pyc,,
narwhals/_arrow/__pycache__/series_dt.cpython-311.pyc,,
narwhals/_arrow/__pycache__/series_list.cpython-311.pyc,,
narwhals/_arrow/__pycache__/series_str.cpython-311.pyc,,
narwhals/_arrow/__pycache__/series_struct.cpython-311.pyc,,
narwhals/_arrow/__pycache__/typing.cpython-311.pyc,,
narwhals/_arrow/__pycache__/utils.cpython-311.pyc,,
narwhals/_arrow/dataframe.py,sha256=VoJ0tXalDwDboGHbxEAlpyaghjkeT9_LwFBtQYtIn4s,28686
narwhals/_arrow/expr.py,sha256=fzEgEwVXETPfoxyvsI7fwTRGuh_t7BNCih0QP-fK4Io,6436
narwhals/_arrow/group_by.py,sha256=SkDRYpKaZXkwxtC-5s1yinBSgVgj2KoAiFFpjSvo9Fo,6458
narwhals/_arrow/namespace.py,sha256=9YWEgy-LN1CgfHHcR1oXTaL2rdsgXIhLWLNABoooLZg,11966
narwhals/_arrow/selectors.py,sha256=qIfCnMNlQ5svQzGaB-DV5YE4xSaUaVzElTPYJl_0BJc,1128
narwhals/_arrow/series.py,sha256=ykSo51a6YCViyd0cXY3u10t21Rb77hx3fDE4mMCe214,45006
narwhals/_arrow/series_cat.py,sha256=vvNlPaHHcA-ORzh_79-oY03wt6aIg1rLI0At8FXr2Ok,598
narwhals/_arrow/series_dt.py,sha256=tTJg3Kxde356LNmqfHOHseWkNofyigFu7s17EtEYTQs,8922
narwhals/_arrow/series_list.py,sha256=hhIE7wZGVQs-J9iX-RyP4sedZ413fStDDj2aW006ALI,647
narwhals/_arrow/series_str.py,sha256=iouTrb8GkJJlZc2ImMLRt8Knh3SvuygVmqORrJc_FSA,3998
narwhals/_arrow/series_struct.py,sha256=85pQSUqOdeMyjsnjaSr_4YBC2HRGD-dsnNy2tPveJRM,410
narwhals/_arrow/typing.py,sha256=TmgG8eqF4uCRW5NFzWTiBvlUGvD46govtIC8gRyrkmA,2286
narwhals/_arrow/utils.py,sha256=KHNaGBGuA1wvrsCrPWR7xv9XaO0iXHgJPuaYrG-PfRI,16121
narwhals/_compliant/__init__.py,sha256=fN7ey51bkGcJ2D3G-SmcEXQWA1nwt9LI2xEW4MYP1rY,2466
narwhals/_compliant/__pycache__/__init__.cpython-311.pyc,,
narwhals/_compliant/__pycache__/any_namespace.cpython-311.pyc,,
narwhals/_compliant/__pycache__/column.cpython-311.pyc,,
narwhals/_compliant/__pycache__/dataframe.cpython-311.pyc,,
narwhals/_compliant/__pycache__/expr.cpython-311.pyc,,
narwhals/_compliant/__pycache__/group_by.cpython-311.pyc,,
narwhals/_compliant/__pycache__/namespace.cpython-311.pyc,,
narwhals/_compliant/__pycache__/selectors.cpython-311.pyc,,
narwhals/_compliant/__pycache__/series.cpython-311.pyc,,
narwhals/_compliant/__pycache__/typing.cpython-311.pyc,,
narwhals/_compliant/__pycache__/when_then.cpython-311.pyc,,
narwhals/_compliant/__pycache__/window.cpython-311.pyc,,
narwhals/_compliant/any_namespace.py,sha256=kXBY2yAN8Oz5E7k-vlq_aQHwHVEmJtYZbL_BgG089xo,3698
narwhals/_compliant/column.py,sha256=mQdztLvvfOi0Su8j2Fh03MOXcphjWL3K2wB9hSHEa-o,7449
narwhals/_compliant/dataframe.py,sha256=mlc5zlkA3ATXehfkzklMTDlN1xqzUr6h2DfAqVSNjMI,16503
narwhals/_compliant/expr.py,sha256=4-ZQ0PhJrIImjqbkeNU5SkdciXOXJvF5rFSGNyP3PD0,40255
narwhals/_compliant/group_by.py,sha256=MjfObbW5ecjywoa4stmoM_Ixcxz3bXtpaW8oWCxP9JQ,7029
narwhals/_compliant/namespace.py,sha256=hP0zh5vgWI7Vh4dmD95ttb3nnpk76wLPZgPhGqAcubI,7171
narwhals/_compliant/selectors.py,sha256=iPONN0RUkGpROkUkdaPujLrwddcEDMjRyBJIPxhbN_o,11784
narwhals/_compliant/series.py,sha256=z0ktqZ2LsIlYV9jTxPPX4AngZ5iyJfuv1XmOwFxhzdE,13819
narwhals/_compliant/typing.py,sha256=YEKLdlkM7Zka4sTBVeLOhwzN20XFoUJiwtWAP-3zjFA,7340
narwhals/_compliant/when_then.py,sha256=hY2O8dNYUaa-9OTUzYYfrzmQp0w13cEf0GtV1hKAiWs,4323
narwhals/_compliant/window.py,sha256=_ji4goVKkT4YPTyZa_I0N2yGmwBfB1_LDG0WSXGbmlo,505
narwhals/_constants.py,sha256=kE1KWsIky4ryabH-Z117ZtGW24ccTcreWOZJjpacO6I,1094
narwhals/_dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_dask/__pycache__/__init__.cpython-311.pyc,,
narwhals/_dask/__pycache__/dataframe.cpython-311.pyc,,
narwhals/_dask/__pycache__/expr.cpython-311.pyc,,
narwhals/_dask/__pycache__/expr_dt.cpython-311.pyc,,
narwhals/_dask/__pycache__/expr_str.cpython-311.pyc,,
narwhals/_dask/__pycache__/group_by.cpython-311.pyc,,
narwhals/_dask/__pycache__/namespace.cpython-311.pyc,,
narwhals/_dask/__pycache__/selectors.cpython-311.pyc,,
narwhals/_dask/__pycache__/utils.cpython-311.pyc,,
narwhals/_dask/dataframe.py,sha256=zTLRoHh9zf4awiU3gZGWSb9-z15Dn_K7O0Ud3tWa8Oc,18146
narwhals/_dask/expr.py,sha256=X-LkBxNmN3cMbjGHaH1AelD5E_mAjoSOa8QRzy3pvNs,26241
narwhals/_dask/expr_dt.py,sha256=7vaSQPZIWsyQsZVP0vN9_60hP6bOI0WP5UDF7jksl_Y,6886
narwhals/_dask/expr_str.py,sha256=SrDcJq_3rHvx1jfQcfi07oS0SGnVkcLE6Xu3uPZfkuA,3558
narwhals/_dask/group_by.py,sha256=w-NNu2gclRKKiRDVxnDiIE6-Wm5nM7c5NMRFXxdLGoA,4906
narwhals/_dask/namespace.py,sha256=I8Kx-iJ9VI1Jrg00M-eZrDN4DsKLtCbYmClR5WWwBXk,13193
narwhals/_dask/selectors.py,sha256=FafFcfFWM6uTcKUsEeqfbmBUIgYVzH5XdN6sFUVLMKU,1148
narwhals/_dask/utils.py,sha256=qdsSkVId_G6i778nfWEl5xqb1Kaq4MjkhGmUGG0eBnY,5484
narwhals/_duckdb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_duckdb/__pycache__/__init__.cpython-311.pyc,,
narwhals/_duckdb/__pycache__/dataframe.cpython-311.pyc,,
narwhals/_duckdb/__pycache__/expr.cpython-311.pyc,,
narwhals/_duckdb/__pycache__/expr_dt.cpython-311.pyc,,
narwhals/_duckdb/__pycache__/expr_list.cpython-311.pyc,,
narwhals/_duckdb/__pycache__/expr_str.cpython-311.pyc,,
narwhals/_duckdb/__pycache__/expr_struct.cpython-311.pyc,,
narwhals/_duckdb/__pycache__/group_by.cpython-311.pyc,,
narwhals/_duckdb/__pycache__/namespace.cpython-311.pyc,,
narwhals/_duckdb/__pycache__/selectors.cpython-311.pyc,,
narwhals/_duckdb/__pycache__/series.cpython-311.pyc,,
narwhals/_duckdb/__pycache__/typing.cpython-311.pyc,,
narwhals/_duckdb/__pycache__/utils.cpython-311.pyc,,
narwhals/_duckdb/dataframe.py,sha256=mzZWCyZ_BlBxWOQMMYk1M5SkA9FuqqxGRJ59cyPrxKs,20340
narwhals/_duckdb/expr.py,sha256=CGajgB01QGGr-24pnEW1kdL3VULrJmfI43aLYWz7wa4,10657
narwhals/_duckdb/expr_dt.py,sha256=QoNbABk0aIuRNyIa790HvisfB177_Ds1H_xMiZWNnHM,4990
narwhals/_duckdb/expr_list.py,sha256=gXPHQZ3oqliATMLye7JugEar-SKOTliCYkfjKv6KZBM,1326
narwhals/_duckdb/expr_str.py,sha256=M7UTLjnHI66I7XYGECORpsJwrrYaYUxyesK2NqGGuok,999
narwhals/_duckdb/expr_struct.py,sha256=eN06QA1JS6wjAt7_AZzW3xoztHM_hoadlFUl_hwsEiE,576
narwhals/_duckdb/group_by.py,sha256=nuueeiJYRcs31Ja973VvtLbWM2wnms0GYL7kAHDeju0,1123
narwhals/_duckdb/namespace.py,sha256=XyTCDc-sdSCdtasrqbkJhFviY375Y2fW_CvCNb0LtPo,5476
narwhals/_duckdb/selectors.py,sha256=yA16Z-MlJUJBjOu0XI9qVO4Zx7L_T5FN2DQqNAYhu-o,1033
narwhals/_duckdb/series.py,sha256=xBpuPUnSSIQ1vYEKjHQFZN7ix1ZyMwSchliDPpkf3Wk,1397
narwhals/_duckdb/typing.py,sha256=gO_Odyinkn4QZY_TU4uuzda6mbeo38glOOUUripcWgg,454
narwhals/_duckdb/utils.py,sha256=maDE0jp7p0nwA-PHDZ4FbBnsJEV0sNBgiuXFghGB0k4,13618
narwhals/_duration.py,sha256=WGzj3FVcC2KogqRhNeim3YDIwUn8HkXQHAljtvHrjwQ,3139
narwhals/_enum.py,sha256=sUR-04yIwjAMsX5eelKnc1UKXc5dBoj1do0krubAE04,1192
narwhals/_exceptions.py,sha256=OhT0MiQbcw_wE85Bl1YYZJjvtlX0rJMNUoZtKNCjTq8,1928
narwhals/_expression_parsing.py,sha256=euJMwE5dgbo7GPIRny-xBhgcJeosL27vN-Q07X55Ysw,23264
narwhals/_ibis/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_ibis/__pycache__/__init__.cpython-311.pyc,,
narwhals/_ibis/__pycache__/dataframe.cpython-311.pyc,,
narwhals/_ibis/__pycache__/expr.cpython-311.pyc,,
narwhals/_ibis/__pycache__/expr_dt.cpython-311.pyc,,
narwhals/_ibis/__pycache__/expr_list.cpython-311.pyc,,
narwhals/_ibis/__pycache__/expr_str.cpython-311.pyc,,
narwhals/_ibis/__pycache__/expr_struct.cpython-311.pyc,,
narwhals/_ibis/__pycache__/group_by.cpython-311.pyc,,
narwhals/_ibis/__pycache__/namespace.cpython-311.pyc,,
narwhals/_ibis/__pycache__/selectors.cpython-311.pyc,,
narwhals/_ibis/__pycache__/series.cpython-311.pyc,,
narwhals/_ibis/__pycache__/utils.cpython-311.pyc,,
narwhals/_ibis/dataframe.py,sha256=mzQwYYebFYJzJaf8DNcukwRpQ78yb4FrNep5-h3fz70,16660
narwhals/_ibis/expr.py,sha256=opNBB2z7yGGeYvIGsarREUvygTNa8-IptktQ2J1r9N4,13077
narwhals/_ibis/expr_dt.py,sha256=2sDgjR5HalXE3IBUc7LvIe4QPjFCnafJPs9ZikbW5xw,3314
narwhals/_ibis/expr_list.py,sha256=TSfb_4EKRdTFIbZ2VJ9zqXJl62ZDkivweK5BiUWFsBc,948
narwhals/_ibis/expr_str.py,sha256=y36FonuKUe5cGUT-wAQriy_4sR-bbb_kPfMBCisomHk,2592
narwhals/_ibis/expr_struct.py,sha256=FDsa5MqcHhqPmpZIEfGBASdqxPkyImrlGTH7XUSw3cs,565
narwhals/_ibis/group_by.py,sha256=enNzAPUsA_LIwPNJ7jG_MJKyqG2HyCiesBEX3pJgJBg,1031
narwhals/_ibis/namespace.py,sha256=0hRod5QixeON0gr4XCqEJrHh3Wa3JK9_4Dz7MTJlFbI,5521
narwhals/_ibis/selectors.py,sha256=SkFxoukpKc_OjwKoKHRm8VwMaphCMUeWBJ2g_oWz3D0,961
narwhals/_ibis/series.py,sha256=CZDwDPsdELKtdr7OWmcFyGqexr33Ucfnv_RU95VJxIQ,1218
narwhals/_ibis/utils.py,sha256=C77hUPh-gl07T31XUHLy1YtSX7nlKvHfpjtQHn007w0,9288
narwhals/_interchange/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_interchange/__pycache__/__init__.cpython-311.pyc,,
narwhals/_interchange/__pycache__/dataframe.cpython-311.pyc,,
narwhals/_interchange/__pycache__/series.cpython-311.pyc,,
narwhals/_interchange/dataframe.py,sha256=FC_2WuxxI4RPUBMqcau68rkdfsxhClou4zCErx1flcw,6034
narwhals/_interchange/series.py,sha256=nSxdlOZrw3wtavS42TMR_b_EGgPBv224ioZBMo5eoC8,1651
narwhals/_namespace.py,sha256=Iw-7TssbXM1vAmpYsB_S9y0mn0K-gwMzBX-LgH0foxM,13708
narwhals/_pandas_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_pandas_like/__pycache__/__init__.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/dataframe.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/expr.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/group_by.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/namespace.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/selectors.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/series.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/series_cat.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/series_dt.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/series_list.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/series_str.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/series_struct.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/typing.cpython-311.pyc,,
narwhals/_pandas_like/__pycache__/utils.cpython-311.pyc,,
narwhals/_pandas_like/dataframe.py,sha256=cYIvOeWPxUxXXW-hVJgQ7On8gBggi9Q1cIGsZBMSDrI,42317
narwhals/_pandas_like/expr.py,sha256=TH1R3ySx2MAAopASc6vW9wIa3DYIaVH7hBsM-Yn6kK8,14833
narwhals/_pandas_like/group_by.py,sha256=T_o11xywuFCyKUA10wQfTyrh82wPHF3Aka29QL_d7vk,13544
narwhals/_pandas_like/namespace.py,sha256=a9PjhoTOBj2-iWz7RXLrC-smArTzC_HRpGQ10Z9_fSU,16916
narwhals/_pandas_like/selectors.py,sha256=Qf7r0H6R8cniwDwC2zlWxddsPx-AHFsZwDPQ9iCEiH8,1261
narwhals/_pandas_like/series.py,sha256=DRqqp20sREqwdsCsk2cKDJPYGc7Y9DjDkDyRVPi_lTw,43404
narwhals/_pandas_like/series_cat.py,sha256=MJwCnJ49hfnODh6JgMHOCQ2KBlTbmySU6_X4XWaqiz4,527
narwhals/_pandas_like/series_dt.py,sha256=EnNPp6StDmwDX9p3mlQw3m4pZWOp0UA1Zh5bJr-gOZE,11587
narwhals/_pandas_like/series_list.py,sha256=xc9m4c5ftCQPfiTy7EujhfNHn7KHbjBUNa-iXHdV9t8,1391
narwhals/_pandas_like/series_str.py,sha256=r_iqLsVZt29ZqGKKcdHupqlror_C8VDU04twU48L3dc,3680
narwhals/_pandas_like/series_struct.py,sha256=vX9HoO42vHackvVozUfp8odM9uJ4owct49ydKDnohdk,518
narwhals/_pandas_like/typing.py,sha256=Awm2YnewvdA3l_4SEwb_5AithhwBYNx1t1ajaHnvUsM,1064
narwhals/_pandas_like/utils.py,sha256=x7DPArSs6CQPc211udUl6eYp_xFWp3xXVSnMRyWCLWM,25293
narwhals/_polars/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_polars/__pycache__/__init__.cpython-311.pyc,,
narwhals/_polars/__pycache__/dataframe.cpython-311.pyc,,
narwhals/_polars/__pycache__/expr.cpython-311.pyc,,
narwhals/_polars/__pycache__/group_by.cpython-311.pyc,,
narwhals/_polars/__pycache__/namespace.cpython-311.pyc,,
narwhals/_polars/__pycache__/series.cpython-311.pyc,,
narwhals/_polars/__pycache__/typing.cpython-311.pyc,,
narwhals/_polars/__pycache__/utils.cpython-311.pyc,,
narwhals/_polars/dataframe.py,sha256=wj8aS_YY5aYXaEnYMMQ00TULtN9qYLB-eJKs39zt6KU,22661
narwhals/_polars/expr.py,sha256=aty5HUPngovw35XvIQBE4ZSvsQGNiGm_VG55rPMrAgg,16538
narwhals/_polars/group_by.py,sha256=v88hD-rOCNtCeT_YqMVII2V1c1B5TEwd0s6qOa1yXb4,2491
narwhals/_polars/namespace.py,sha256=Oshgk1AvrKAa2TIushOzATYmUG1931JCAD0TpCH4byE,9656
narwhals/_polars/series.py,sha256=OdpVFRszIK8VB7TBh9YbOgaf2lBa4BpuT5aeKKUtdEs,26129
narwhals/_polars/typing.py,sha256=tiAgtciFZmlqqH3Q6MdQLXZEb1ajH-YbePpaKjeuqQE,786
narwhals/_polars/utils.py,sha256=hIjAce1DOez6ZEJPBbY4x_Pl4wGubdnbye5H6tE0_DA,12386
narwhals/_spark_like/__init__.py,sha256=T7_UCePrrYs1ZeMatvCUYUvQcXvrDjQ4b08_ugWIHAo,87
narwhals/_spark_like/__pycache__/__init__.cpython-311.pyc,,
narwhals/_spark_like/__pycache__/dataframe.cpython-311.pyc,,
narwhals/_spark_like/__pycache__/expr.cpython-311.pyc,,
narwhals/_spark_like/__pycache__/expr_dt.cpython-311.pyc,,
narwhals/_spark_like/__pycache__/expr_list.cpython-311.pyc,,
narwhals/_spark_like/__pycache__/expr_str.cpython-311.pyc,,
narwhals/_spark_like/__pycache__/expr_struct.cpython-311.pyc,,
narwhals/_spark_like/__pycache__/group_by.cpython-311.pyc,,
narwhals/_spark_like/__pycache__/namespace.cpython-311.pyc,,
narwhals/_spark_like/__pycache__/selectors.cpython-311.pyc,,
narwhals/_spark_like/__pycache__/utils.cpython-311.pyc,,
narwhals/_spark_like/dataframe.py,sha256=Aa5q0Tq-baUnhucIq4M031m_HD8qWylaAewi--UZmoY,22190
narwhals/_spark_like/expr.py,sha256=q1HuJyoSDqQBjznT2hrDyakvaUFrBl95MYo1yrtP4-Y,14098
narwhals/_spark_like/expr_dt.py,sha256=1ICwI2xpTuK5PyGjiLEdfLgsgudDPEVdMGgVnM_c5Uk,7515
narwhals/_spark_like/expr_list.py,sha256=aU29eGRQ89F7z5XticN9-l7ShVJpD2Ni4rdxE9Wfd7w,1132
narwhals/_spark_like/expr_str.py,sha256=eGN7uW9BfP7-KVey-RrqFcQjZrO7WnTlU3PZPpPSzKk,1298
narwhals/_spark_like/expr_struct.py,sha256=haBDpuRhn_nGAFjMF3arhhRr6NfefNei9vEmAOa0fQI,613
narwhals/_spark_like/group_by.py,sha256=rsAhSHEoA1pHzPk--9xtKvLJbTHOtJ45ftVKUhI7KUc,1246
narwhals/_spark_like/namespace.py,sha256=rFliViMHZ03H22K9eYCPQ9B-hbiGpHnJII_0gWbOnc8,8027
narwhals/_spark_like/selectors.py,sha256=SzJPoFjyIEviSSvPRvL81o1jjQJcM-Veqb52vFU66JQ,1086
narwhals/_spark_like/utils.py,sha256=ViTlbGgfG5koBKHtO5sCACFSFD94iTJtNGTUETOEBLw,11367
narwhals/_sql/__init__.py,sha256=T7_UCePrrYs1ZeMatvCUYUvQcXvrDjQ4b08_ugWIHAo,87
narwhals/_sql/__pycache__/__init__.cpython-311.pyc,,
narwhals/_sql/__pycache__/dataframe.cpython-311.pyc,,
narwhals/_sql/__pycache__/expr.cpython-311.pyc,,
narwhals/_sql/__pycache__/expr_dt.cpython-311.pyc,,
narwhals/_sql/__pycache__/expr_str.cpython-311.pyc,,
narwhals/_sql/__pycache__/group_by.cpython-311.pyc,,
narwhals/_sql/__pycache__/namespace.cpython-311.pyc,,
narwhals/_sql/__pycache__/typing.cpython-311.pyc,,
narwhals/_sql/__pycache__/when_then.cpython-311.pyc,,
narwhals/_sql/dataframe.py,sha256=RgmNIcJ2r4JIurB64vzqo0-qPUJH9kBFo4hT1gvqB0w,1491
narwhals/_sql/expr.py,sha256=LZ-ZAAy2-dUztzccoC4TxHPtFIXd_zd-4NO9cQKxCGc,29314
narwhals/_sql/expr_dt.py,sha256=CWFn7Ki3YW3XT_Hy88pdCTZs8j6jP4GpP38vgPd-vX4,1612
narwhals/_sql/expr_str.py,sha256=XNFamhOoxX9yW_zr2BEDXukzjWMCGL-yy3m5qW9pVm0,5061
narwhals/_sql/group_by.py,sha256=4n9WptpH3GzNvqaSgczdYmQ0DIS35g3ioSLi3R3ahq0,1681
narwhals/_sql/namespace.py,sha256=2KRj7AnNfWEpWbjkl2X5pCya9jrq8jLwKMKFp32GFI0,2755
narwhals/_sql/typing.py,sha256=e3LkLPI4oa2IzykR7BgO9IIfCKRw0vrX4uHxPTB-uJM,487
narwhals/_sql/when_then.py,sha256=4lXcc_J_N6vHGby6kPJl1PGqLPUGbgHYuIXiYROyoW4,3636
narwhals/_translate.py,sha256=e8RjNCNX4QGJWKjM6VANDTG_bVT2VusjNfjsnkCBO3g,6112
narwhals/_typing.py,sha256=UZpLQSfFBNLLFr6ZW24D_9LJhVhrmKSUTMJzbj8aZ3M,6534
narwhals/_typing_compat.py,sha256=h-BtLEl7CrZ-hYLlwYTOcCdsUS3dvgvkxQTcDQ7RYmA,2516
narwhals/_utils.py,sha256=hfStaW1LliFEgicxvARwE2M3VnHsq7U5I5okNeElY6U,67185
narwhals/dataframe.py,sha256=-90Y4HrSX_Yl94eF-XUuNS3hHLa-jYpyXO2QnewvUck,134807
narwhals/dependencies.py,sha256=C_dqrNq9GCN1gcv18PwF7ALIjF7sHiCvWLqYxUVLnRk,18766
narwhals/dtypes.py,sha256=3N-VO1ZaFHCTf_JDaFjFJYQVJwYK57UD2m_cmRzCjMk,29613
narwhals/exceptions.py,sha256=9ocrbLNP7fZLqP2gV1PS9OexNhzf8h7of2wR5wi5kE0,3704
narwhals/expr.py,sha256=yBRW8RtCMvFeHO01ISGSxOpuPjwSXaT4ic52hxZQoL0,98786
narwhals/expr_cat.py,sha256=o4MhGmPoO3_DlkRB06Z3ilyqyj70mwcW3kftRayDq2s,1210
narwhals/expr_dt.py,sha256=R3F9z9FAluZBZ7ETt2lOdkMrq_OmG2YMYBpkIkGzUQc,32465
narwhals/expr_list.py,sha256=8-_L7wzxm90kcRooFW2QEdzn0FgJNMnUispBReBE5fs,6744
narwhals/expr_name.py,sha256=0QD8Yg7FKu739ENSJI2lxIGSjho89J9l9DjxeBOz9bM,4866
narwhals/expr_str.py,sha256=PoSIJB9R9l-RPwEXkchMbxonpc0N4Ppgb5bAkrUALL4,19519
narwhals/expr_struct.py,sha256=V_Hj3kChdcZPIlfb_Kslp5W-j-XGFcfdMFzYpZdjNWE,1742
narwhals/functions.py,sha256=9b3rUkRnZ77heRlafbJt8LP-zTwjWCqHO8lw6d2T7Ds,64441
narwhals/group_by.py,sha256=7UkbFvCZ6n0rtgDovbElueEA8p4oS3i3ny_g5TGabek,7164
narwhals/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/schema.py,sha256=9CiVV31floJJslais5FVUF0DkKCaXeDKOyk9XT8kwGk,5637
narwhals/selectors.py,sha256=ybbFG7Sjebr8qoMgD43O6QuHBGl52yUpGRe08L1LKyo,10759
narwhals/series.py,sha256=IIMiPTJMlBNv3kc6QDpxqBr0Oaetxb5WbZZOktaJldw,95125
narwhals/series_cat.py,sha256=KU5DMtCqi0KKVrmTfCLpgI32AGuY3MYZclNF6soh1Xc,834
narwhals/series_dt.py,sha256=a7JIMtA_Wn9ZiBa9O_-t7k6Lk-Az2AhHbQYlS1VwxAA,23051
narwhals/series_list.py,sha256=PMXSEL_Pai2ZoMcNi0KZ6WdXHlMvTVyFK600TTGhCeg,3802
narwhals/series_str.py,sha256=yT6DD17wR0vspZT7KaIvG3Fav2nHJ9JuxkTD5pqPOj0,15133
narwhals/series_struct.py,sha256=bixxdASlxYyP1prDjMmfltVU5212a596VQZE11yszUg,930
narwhals/stable/__init__.py,sha256=b9soCkGkQzgF5jO5EdQ6IOQpnc6G6eqWmY6WwpoSjhk,85
narwhals/stable/__pycache__/__init__.cpython-311.pyc,,
narwhals/stable/v1/__init__.py,sha256=uM2Q3IjXxm34WNgF42854e0Z-Cky6pLvffVcmspTkYg,43282
narwhals/stable/v1/__pycache__/__init__.cpython-311.pyc,,
narwhals/stable/v1/__pycache__/_dtypes.cpython-311.pyc,,
narwhals/stable/v1/__pycache__/_namespace.cpython-311.pyc,,
narwhals/stable/v1/__pycache__/dependencies.cpython-311.pyc,,
narwhals/stable/v1/__pycache__/dtypes.cpython-311.pyc,,
narwhals/stable/v1/__pycache__/selectors.cpython-311.pyc,,
narwhals/stable/v1/__pycache__/typing.cpython-311.pyc,,
narwhals/stable/v1/_dtypes.py,sha256=7zGmarnurUTgY6DI4KQ1MSAC7B9ZZiI5Em7plb-HAEs,2700
narwhals/stable/v1/_namespace.py,sha256=gfsbT4R4aLmmdArY35LRpEHPiUeZKEEnXGiY9ypFtwE,296
narwhals/stable/v1/dependencies.py,sha256=aM0IShF4hbaaMEDRJQXvsu4RABZOdBG4QhrpJPxb7fg,5001
narwhals/stable/v1/dtypes.py,sha256=u2NFDJyCkjsK6p3K9ULJS7CoG16z0Z1MQiACTVkhkH4,1082
narwhals/stable/v1/selectors.py,sha256=xEA9bBzkpTwUanGGoFwBCcHIAXb8alwrPX1mjzE9mDM,312
narwhals/stable/v1/typing.py,sha256=7dBQVxmW6zQOjfT2N0rEddKUBD61roxhZJDJhApC0fk,6433
narwhals/stable/v2/__init__.py,sha256=hyCy4Kin5mrbU-_EnSRT548IvDlP5X913pa5d2pMcq4,40334
narwhals/stable/v2/__pycache__/__init__.cpython-311.pyc,,
narwhals/stable/v2/__pycache__/_namespace.cpython-311.pyc,,
narwhals/stable/v2/__pycache__/dependencies.cpython-311.pyc,,
narwhals/stable/v2/__pycache__/dtypes.cpython-311.pyc,,
narwhals/stable/v2/__pycache__/selectors.cpython-311.pyc,,
narwhals/stable/v2/__pycache__/typing.cpython-311.pyc,,
narwhals/stable/v2/_namespace.py,sha256=oYB5nrFGxqqTonkRx9vUanyBxGs2Yb0j7_juMyvnvWA,296
narwhals/stable/v2/dependencies.py,sha256=vpYWx_dron6wFdbQ60G06EV2UJ_LMd52LDodCrAY5Jg,86
narwhals/stable/v2/dtypes.py,sha256=iMpk2Kc1mNiQYmboOSgmiAijklSUBHSHF2LTKMKnGe8,80
narwhals/stable/v2/selectors.py,sha256=sjJL3agHd8Rgf_lWhgCmEKruhWEkwHdX32-n85OqVJU,83
narwhals/stable/v2/typing.py,sha256=mymfP7wg6FfyunGiK51I-jqEIcPA5u8gFOHN4sDoz5I,6042
narwhals/this.py,sha256=BbKcj0ReWqE01lznzKjuqq7otXONvjBevWWC5aJhQxs,1584
narwhals/translate.py,sha256=Mscw6Nh5yUgkKqWyEEst205vbfdskOUa9DlouVe2ThA,24741
narwhals/typing.py,sha256=ZIjw3bC_0Em-4WmrocH2RDShA3ZgWo4aZ3vwjYinhW8,16783
narwhals/utils.py,sha256=2GT3XxucWI6l9r9jTwMw7Aha2G73FsSXgXNFZ3O_ZyA,223
