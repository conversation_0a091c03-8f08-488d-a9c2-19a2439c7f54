import sys
from typing import NoReturn

version: str

class ErrorHandler:
    def error(self, exception: BaseException) -> NoReturn: ...
    def fatalError(self, exception: BaseException) -> NoReturn: ...
    def warning(self, exception: BaseException) -> None: ...

class ContentHandler:
    def setDocumentLocator(self, locator): ...
    def startDocument(self): ...
    def endDocument(self): ...
    def startPrefixMapping(self, prefix, uri): ...
    def endPrefixMapping(self, prefix): ...
    def startElement(self, name, attrs): ...
    def endElement(self, name): ...
    def startElementNS(self, name, qname, attrs): ...
    def endElementNS(self, name, qname): ...
    def characters(self, content): ...
    def ignorableWhitespace(self, whitespace): ...
    def processingInstruction(self, target, data): ...
    def skippedEntity(self, name): ...

class DTDHandler:
    def notationDecl(self, name, publicId, systemId): ...
    def unparsedEntityDecl(self, name, publicId, systemId, ndata): ...

class EntityResolver:
    def resolveEntity(self, publicId, systemId): ...

feature_namespaces: str
feature_namespace_prefixes: str
feature_string_interning: str
feature_validation: str
feature_external_ges: str
feature_external_pes: str
all_features: list[str]
property_lexical_handler: str
property_declaration_handler: str
property_dom_node: str
property_xml_string: str
property_encoding: str
property_interning_dict: str
all_properties: list[str]

if sys.version_info >= (3, 10):
    class LexicalHandler:
        def comment(self, content: str) -> object: ...
        def startDTD(self, name: str, public_id: str | None, system_id: str | None) -> object: ...
        def endDTD(self) -> object: ...
        def startCDATA(self) -> object: ...
        def endCDATA(self) -> object: ...
