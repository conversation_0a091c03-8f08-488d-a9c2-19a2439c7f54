from collections.abc import Iterator
from typing import Any, Protocol, TypeVar

_T = TypeVar("_T")

class PackageMetadata(Protocol):
    def __len__(self) -> int: ...
    def __contains__(self, item: str) -> bool: ...
    def __getitem__(self, key: str) -> str: ...
    def __iter__(self) -> Iterator[str]: ...
    def get_all(self, name: str, failobj: _T = ...) -> list[Any] | _T: ...
    @property
    def json(self) -> dict[str, str | list[str]]: ...

class SimplePath(Protocol):
    def joinpath(self) -> SimplePath: ...
    def parent(self) -> SimplePath: ...
    def read_text(self) -> str: ...
    # There was a bug in `SimplePath` definition in cpython, see #8451
    #  Strictly speaking `__div__` was defined in 3.10, not __truediv__,
    # but it should have always been `__truediv__`.
    def __truediv__(self) -> SimplePath: ...
