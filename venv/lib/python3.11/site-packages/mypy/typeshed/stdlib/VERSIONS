# The structure of this file is as follows:
# - Blank lines and comments starting with `#` are ignored.
# - Lines contain the name of a module, followed by a colon,
#   a space, and a version range (for example: `symbol: 2.7-3.9`).
#
# Version ranges may be of the form "X.Y-A.B" or "X.Y-". The
# first form means that a module was introduced in version X.Y and last
# available in version A.B. The second form means that the module was
# introduced in version X.Y and is still available in the latest
# version of Python.
#
# If a submodule is not listed separately, it has the same lifetime as
# its parent module.
#
# Python versions before 2.7 are ignored, so any module that was already
# present in 2.7 will have "2.7" as its minimum version. Version ranges
# for unsupported versions of Python 3 are generally accurate but we do
# not guarantee their correctness.

__future__: 2.7-
__main__: 2.7-
_ast: 2.7-
_bisect: 2.7-
_bootlocale: 3.4-3.9
_codecs: 2.7-
_collections_abc: 3.3-
_compat_pickle: 3.1-
_compression: 3.5-
_csv: 2.7-
_ctypes: 2.7-
_curses: 2.7-
_decimal: 3.3-
_dummy_thread: 3.0-3.8
_dummy_threading: 2.7-3.8
_heapq: 2.7-
_imp: 3.0-
_json: 2.7-
_markupbase: 2.7-
_msi: 2.7-
_operator: 3.4-
_osx_support: 2.7-
_posixsubprocess: 3.2-
_py_abc: 3.7-
_pydecimal: 3.5-
_random: 2.7-
_sitebuiltins: 3.4-
_socket: 3.0-  # present in 2.7 at runtime, but not in typeshed
_stat: 3.4-
_thread: 2.7-
_threading_local: 2.7-
_tkinter: 2.7-
_tracemalloc: 3.4-
_typeshed: 2.7-  # not present at runtime, only for type checking
_warnings: 2.7-
_weakref: 2.7-
_weakrefset: 2.7-
_winapi: 3.3-
abc: 2.7-
aifc: 2.7-
antigravity: 2.7-
argparse: 2.7-
array: 2.7-
ast: 2.7-
asynchat: 2.7-3.11
asyncio: 3.4-
asyncio.mixins: 3.10-
asyncio.exceptions: 3.8-
asyncio.format_helpers: 3.7-
asyncio.runners: 3.7-
asyncio.staggered: 3.8-
asyncio.taskgroups: 3.11-
asyncio.threads: 3.9-
asyncio.timeouts: 3.11-
asyncio.trsock: 3.8-
asyncore: 2.7-3.11
atexit: 2.7-
audioop: 2.7-
base64: 2.7-
bdb: 2.7-
binascii: 2.7-
binhex: 2.7-3.10
bisect: 2.7-
builtins: 3.0-
bz2: 2.7-
cProfile: 2.7-
calendar: 2.7-
cgi: 2.7-
cgitb: 2.7-
chunk: 2.7-
cmath: 2.7-
cmd: 2.7-
code: 2.7-
codecs: 2.7-
codeop: 2.7-
collections: 2.7-
collections.abc: 3.3-
colorsys: 2.7-
compileall: 2.7-
concurrent: 3.2-
configparser: 3.0-
contextlib: 2.7-
contextvars: 3.7-
copy: 2.7-
copyreg: 2.7-
crypt: 2.7-
csv: 2.7-
ctypes: 2.7-
curses: 2.7-
dataclasses: 3.7-
datetime: 2.7-
dbm: 2.7-
decimal: 2.7-
difflib: 2.7-
dis: 2.7-
distutils: 2.7-3.11
distutils.command.bdist_msi: 2.7-3.10
distutils.command.bdist_wininst: 2.7-3.9
doctest: 2.7-
dummy_threading: 2.7-3.8
email: 2.7-
encodings: 2.7-
ensurepip: 2.7-
enum: 3.4-
errno: 2.7-
faulthandler: 3.3-
fcntl: 2.7-
filecmp: 2.7-
fileinput: 2.7-
fnmatch: 2.7-
formatter: 2.7-3.9
fractions: 2.7-
ftplib: 2.7-
functools: 2.7-
gc: 2.7-
genericpath: 2.7-
getopt: 2.7-
getpass: 2.7-
gettext: 2.7-
glob: 2.7-
graphlib: 3.9-
grp: 2.7-
gzip: 2.7-
hashlib: 2.7-
heapq: 2.7-
hmac: 2.7-
html: 3.0-
http: 3.0-
imaplib: 2.7-
imghdr: 2.7-
imp: 2.7-3.11
importlib: 2.7-
importlib.metadata: 3.8-
importlib.metadata._meta: 3.10-
importlib.resources: 3.7-
importlib.resources.abc: 3.11-
inspect: 2.7-
io: 2.7-
ipaddress: 3.3-
itertools: 2.7-
json: 2.7-
keyword: 2.7-
lib2to3: 2.7-
linecache: 2.7-
locale: 2.7-
logging: 2.7-
lzma: 3.3-
macpath: 2.7-3.7
mailbox: 2.7-
mailcap: 2.7-
marshal: 2.7-
math: 2.7-
mimetypes: 2.7-
mmap: 2.7-
modulefinder: 2.7-
msilib: 2.7-
msvcrt: 2.7-
multiprocessing: 2.7-
multiprocessing.resource_tracker: 3.8-
multiprocessing.shared_memory: 3.8-
netrc: 2.7-
nis: 2.7-
nntplib: 2.7-
ntpath: 2.7-
nturl2path: 2.7-
numbers: 2.7-
opcode: 2.7-
operator: 2.7-
optparse: 2.7-
os: 2.7-
ossaudiodev: 2.7-
parser: 2.7-3.9
pathlib: 3.4-
pdb: 2.7-
pickle: 2.7-
pickletools: 2.7-
pipes: 2.7-
pkgutil: 2.7-
platform: 2.7-
plistlib: 2.7-
poplib: 2.7-
posix: 2.7-
posixpath: 2.7-
pprint: 2.7-
profile: 2.7-
pstats: 2.7-
pty: 2.7-
pwd: 2.7-
py_compile: 2.7-
pyclbr: 2.7-
pydoc: 2.7-
pydoc_data: 2.7-
pyexpat: 2.7-
queue: 3.0-
quopri: 2.7-
random: 2.7-
re: 2.7-
readline: 2.7-
reprlib: 3.0-
resource: 2.7-
rlcompleter: 2.7-
runpy: 2.7-
sched: 2.7-
secrets: 3.6-
select: 2.7-
selectors: 3.4-
shelve: 2.7-
shlex: 2.7-
shutil: 2.7-
signal: 2.7-
site: 2.7-
smtpd: 2.7-3.11
smtplib: 2.7-
sndhdr: 2.7-
socket: 2.7-
socketserver: 3.0-
spwd: 2.7-
sqlite3: 2.7-
sre_compile: 2.7-
sre_constants: 2.7-
sre_parse: 2.7-
ssl: 2.7-
stat: 2.7-
statistics: 3.4-
string: 2.7-
stringprep: 2.7-
struct: 2.7-
subprocess: 2.7-
sunau: 2.7-
symbol: 2.7-3.9
symtable: 2.7-
sys: 2.7-
sysconfig: 2.7-
syslog: 2.7-
tabnanny: 2.7-
tarfile: 2.7-
telnetlib: 2.7-
tempfile: 2.7-
termios: 2.7-
textwrap: 2.7-
this: 2.7-
threading: 2.7-
time: 2.7-
timeit: 2.7-
tkinter: 3.0-
token: 2.7-
tokenize: 2.7-
tomllib: 3.11-
trace: 2.7-
traceback: 2.7-
tracemalloc: 3.4-
tty: 2.7-
turtle: 2.7-
types: 2.7-
typing: 3.5-
typing_extensions: 2.7-
unicodedata: 2.7-
unittest: 2.7-
unittest._log: 3.9-
unittest.async_case: 3.8-
urllib: 2.7-
uu: 2.7-
uuid: 2.7-
venv: 3.3-
warnings: 2.7-
wave: 2.7-
weakref: 2.7-
webbrowser: 2.7-
winreg: 3.0-
winsound: 2.7-
wsgiref: 2.7-
wsgiref.types: 3.11-
xdrlib: 2.7-
xml: 2.7-
xmlrpc: 3.0-
xxlimited: 3.2-
zipapp: 3.5-
zipfile: 2.7-
zipimport: 2.7-
zlib: 2.7-
zoneinfo: 3.9-
