import unittest.case
import unittest.loader
import unittest.result
import unittest.suite
from collections.abc import Iterable
from types import ModuleType
from typing import Any, Protocol

MAIN_EXAMPLES: str
MODULE_EXAMPLES: str

class _TestRunner(Protocol):
    def run(self, test: unittest.suite.TestSuite | unittest.case.TestCase) -> unittest.result.TestResult: ...

# not really documented
class TestProgram:
    result: unittest.result.TestResult
    module: None | str | ModuleType
    verbosity: int
    failfast: bool | None
    catchbreak: bool | None
    buffer: bool | None
    progName: str | None
    warnings: str | None
    testNamePatterns: list[str] | None
    def __init__(
        self,
        module: None | str | ModuleType = "__main__",
        defaultTest: str | Iterable[str] | None = None,
        argv: list[str] | None = None,
        testRunner: type[_TestRunner] | _TestRunner | None = None,
        testLoader: unittest.loader.TestLoader = ...,
        exit: bool = True,
        verbosity: int = 1,
        failfast: bool | None = None,
        catchbreak: bool | None = None,
        buffer: bool | None = None,
        warnings: str | None = None,
        *,
        tb_locals: bool = False,
    ) -> None: ...
    def usageExit(self, msg: Any = None) -> None: ...
    def parseArgs(self, argv: list[str]) -> None: ...
    def createTests(self, from_discovery: bool = False, Loader: unittest.loader.TestLoader | None = None) -> None: ...
    def runTests(self) -> None: ...  # undocumented

main = TestProgram
