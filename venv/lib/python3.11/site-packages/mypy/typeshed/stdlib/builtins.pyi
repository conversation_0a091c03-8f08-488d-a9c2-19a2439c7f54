import _ast
import _typeshed
import sys
import types
from _collections_abc import dict_items, dict_keys, dict_values
from _typeshed import (
    AnyStr_co,
    FileDescriptorOrPath,
    OpenBinaryMode,
    OpenBinaryModeReading,
    OpenBinaryModeUpdating,
    OpenBinaryModeWriting,
    OpenTextMode,
    ReadableBuffer,
    SupportsAdd,
    SupportsAiter,
    SupportsAnext,
    SupportsDivMod,
    SupportsIter,
    SupportsKeysAndGetItem,
    SupportsLenAndGetItem,
    SupportsNext,
    SupportsRAdd,
    SupportsRDivMod,
    SupportsRichComparison,
    SupportsRichComparisonT,
    SupportsTrunc,
    SupportsWrite,
)
from collections.abc import Awaitable, Callable, Iterable, Iterator, MutableSet, Reversible, Set as AbstractSet, Sized
from io import BufferedRandom, BufferedReader, BufferedWriter, FileIO, TextIOWrapper
from types import CodeType, TracebackType, _Cell

# mypy crashes if any of {ByteString, Sequence, MutableSequence, Mapping, MutableMapping} are imported from collections.abc in builtins.pyi
from typing import (  # noqa: Y022
    IO,
    Any,
    BinaryIO,
    ClassVar,
    Generic,
    Mapping,
    MutableMapping,
    MutableSequence,
    NoReturn,
    Protocol,
    Sequence,
    SupportsAbs,
    SupportsBytes,
    SupportsComplex,
    SupportsFloat,
    SupportsInt,
    TypeVar,
    overload,
    type_check_only,
)
from typing_extensions import (
    Concatenate,
    Literal,
    ParamSpec,
    Self,
    SupportsIndex,
    TypeAlias,
    TypeGuard,
    TypeVarTuple,
    final,
)

if sys.version_info >= (3, 9):
    from types import GenericAlias

_T = TypeVar("_T")
_T_co = TypeVar("_T_co", covariant=True)
_T_contra = TypeVar("_T_contra", contravariant=True)
_R_co = TypeVar("_R_co", covariant=True)
_KT = TypeVar("_KT")
_VT = TypeVar("_VT")
_S = TypeVar("_S")
_T1 = TypeVar("_T1")
_T2 = TypeVar("_T2")
_T3 = TypeVar("_T3")
_T4 = TypeVar("_T4")
_T5 = TypeVar("_T5")
_SupportsNextT = TypeVar("_SupportsNextT", bound=SupportsNext[Any], covariant=True)
_SupportsAnextT = TypeVar("_SupportsAnextT", bound=SupportsAnext[Any], covariant=True)
_AwaitableT = TypeVar("_AwaitableT", bound=Awaitable[Any])
_AwaitableT_co = TypeVar("_AwaitableT_co", bound=Awaitable[Any], covariant=True)
_P = ParamSpec("_P")

class object:
    __doc__: str | None
    __dict__: dict[str, Any]
    __module__: str
    __annotations__: dict[str, Any]
    @property
    def __class__(self) -> type[Self]: ...
    # Ignore errors about type mismatch between property getter and setter
    @__class__.setter
    def __class__(self, __type: type[object]) -> None: ...  # noqa: F811
    def __init__(self) -> None: ...
    def __new__(cls) -> Self: ...
    # N.B. `object.__setattr__` and `object.__delattr__` are heavily special-cased by type checkers.
    # Overriding them in subclasses has different semantics, even if the override has an identical signature.
    def __setattr__(self, __name: str, __value: Any) -> None: ...
    def __delattr__(self, __name: str) -> None: ...
    def __eq__(self, __value: object) -> bool: ...
    def __ne__(self, __value: object) -> bool: ...
    def __str__(self) -> str: ...  # noqa: Y029
    def __repr__(self) -> str: ...  # noqa: Y029
    def __hash__(self) -> int: ...
    def __format__(self, __format_spec: str) -> str: ...
    def __getattribute__(self, __name: str) -> Any: ...
    def __sizeof__(self) -> int: ...
    # return type of pickle methods is rather hard to express in the current type system
    # see #6661 and https://docs.python.org/3/library/pickle.html#object.__reduce__
    def __reduce__(self) -> str | tuple[Any, ...]: ...
    if sys.version_info >= (3, 8):
        def __reduce_ex__(self, __protocol: SupportsIndex) -> str | tuple[Any, ...]: ...
    else:
        def __reduce_ex__(self, __protocol: int) -> str | tuple[Any, ...]: ...
    if sys.version_info >= (3, 11):
        def __getstate__(self) -> object: ...

    def __dir__(self) -> Iterable[str]: ...
    def __init_subclass__(cls) -> None: ...
    @classmethod
    def __subclasshook__(cls, __subclass: type) -> bool: ...

class staticmethod(Generic[_P, _R_co]):
    @property
    def __func__(self) -> Callable[_P, _R_co]: ...
    @property
    def __isabstractmethod__(self) -> bool: ...
    def __init__(self, __f: Callable[_P, _R_co]) -> None: ...
    @overload
    def __get__(self, __instance: None, __owner: type) -> Callable[_P, _R_co]: ...
    @overload
    def __get__(self, __instance: _T, __owner: type[_T] | None = None) -> Callable[_P, _R_co]: ...
    if sys.version_info >= (3, 10):
        __name__: str
        __qualname__: str
        @property
        def __wrapped__(self) -> Callable[_P, _R_co]: ...
        def __call__(self, *args: _P.args, **kwargs: _P.kwargs) -> _R_co: ...

class classmethod(Generic[_T, _P, _R_co]):
    @property
    def __func__(self) -> Callable[Concatenate[type[_T], _P], _R_co]: ...
    @property
    def __isabstractmethod__(self) -> bool: ...
    def __init__(self, __f: Callable[Concatenate[type[_T], _P], _R_co]) -> None: ...
    @overload
    def __get__(self, __instance: _T, __owner: type[_T] | None = None) -> Callable[_P, _R_co]: ...
    @overload
    def __get__(self, __instance: None, __owner: type[_T]) -> Callable[_P, _R_co]: ...
    if sys.version_info >= (3, 10):
        __name__: str
        __qualname__: str
        @property
        def __wrapped__(self) -> Callable[Concatenate[type[_T], _P], _R_co]: ...

class type:
    @property
    def __base__(self) -> type: ...
    __bases__: tuple[type, ...]
    @property
    def __basicsize__(self) -> int: ...
    @property
    def __dict__(self) -> types.MappingProxyType[str, Any]: ...  # type: ignore[override]
    @property
    def __dictoffset__(self) -> int: ...
    @property
    def __flags__(self) -> int: ...
    @property
    def __itemsize__(self) -> int: ...
    __module__: str
    @property
    def __mro__(self) -> tuple[type, ...]: ...
    __name__: str
    __qualname__: str
    @property
    def __text_signature__(self) -> str | None: ...
    @property
    def __weakrefoffset__(self) -> int: ...
    @overload
    def __init__(self, __o: object) -> None: ...
    @overload
    def __init__(self, __name: str, __bases: tuple[type, ...], __dict: dict[str, Any], **kwds: Any) -> None: ...
    @overload
    def __new__(cls, __o: object) -> type: ...
    @overload
    def __new__(
        cls: type[_typeshed.Self], __name: str, __bases: tuple[type, ...], __namespace: dict[str, Any], **kwds: Any
    ) -> _typeshed.Self: ...
    def __call__(self, *args: Any, **kwds: Any) -> Any: ...
    def __subclasses__(self: _typeshed.Self) -> list[_typeshed.Self]: ...
    # Note: the documentation doesn't specify what the return type is, the standard
    # implementation seems to be returning a list.
    def mro(self) -> list[type]: ...
    def __instancecheck__(self, __instance: Any) -> bool: ...
    def __subclasscheck__(self, __subclass: type) -> bool: ...
    @classmethod
    def __prepare__(metacls, __name: str, __bases: tuple[type, ...], **kwds: Any) -> Mapping[str, object]: ...
    if sys.version_info >= (3, 10):
        def __or__(self, __value: Any) -> types.UnionType: ...
        def __ror__(self, __value: Any) -> types.UnionType: ...
    if sys.version_info >= (3, 12):
        __type_params__: tuple[TypeVar | ParamSpec | TypeVarTuple, ...]

class super:
    @overload
    def __init__(self, __t: Any, __obj: Any) -> None: ...
    @overload
    def __init__(self, __t: Any) -> None: ...
    @overload
    def __init__(self) -> None: ...

_PositiveInteger: TypeAlias = Literal[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]
_NegativeInteger: TypeAlias = Literal[-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20]
_LiteralInteger = _PositiveInteger | _NegativeInteger | Literal[0]  # noqa: Y026  # TODO: Use TypeAlias once mypy bugs are fixed

class int:
    @overload
    def __new__(cls, __x: str | ReadableBuffer | SupportsInt | SupportsIndex | SupportsTrunc = ...) -> Self: ...
    @overload
    def __new__(cls, __x: str | bytes | bytearray, base: SupportsIndex) -> Self: ...
    if sys.version_info >= (3, 8):
        def as_integer_ratio(self) -> tuple[int, Literal[1]]: ...

    @property
    def real(self) -> int: ...
    @property
    def imag(self) -> Literal[0]: ...
    @property
    def numerator(self) -> int: ...
    @property
    def denominator(self) -> Literal[1]: ...
    def conjugate(self) -> int: ...
    def bit_length(self) -> int: ...
    if sys.version_info >= (3, 10):
        def bit_count(self) -> int: ...

    if sys.version_info >= (3, 11):
        def to_bytes(
            self, length: SupportsIndex = 1, byteorder: Literal["little", "big"] = "big", *, signed: bool = False
        ) -> bytes: ...
        @classmethod
        def from_bytes(
            cls,
            bytes: Iterable[SupportsIndex] | SupportsBytes | ReadableBuffer,
            byteorder: Literal["little", "big"] = "big",
            *,
            signed: bool = False,
        ) -> Self: ...
    else:
        def to_bytes(self, length: SupportsIndex, byteorder: Literal["little", "big"], *, signed: bool = False) -> bytes: ...
        @classmethod
        def from_bytes(
            cls,
            bytes: Iterable[SupportsIndex] | SupportsBytes | ReadableBuffer,
            byteorder: Literal["little", "big"],
            *,
            signed: bool = False,
        ) -> Self: ...

    if sys.version_info >= (3, 12):
        def is_integer(self) -> Literal[True]: ...

    def __add__(self, __value: int) -> int: ...
    def __sub__(self, __value: int) -> int: ...
    def __mul__(self, __value: int) -> int: ...
    def __floordiv__(self, __value: int) -> int: ...
    def __truediv__(self, __value: int) -> float: ...
    def __mod__(self, __value: int) -> int: ...
    def __divmod__(self, __value: int) -> tuple[int, int]: ...
    def __radd__(self, __value: int) -> int: ...
    def __rsub__(self, __value: int) -> int: ...
    def __rmul__(self, __value: int) -> int: ...
    def __rfloordiv__(self, __value: int) -> int: ...
    def __rtruediv__(self, __value: int) -> float: ...
    def __rmod__(self, __value: int) -> int: ...
    def __rdivmod__(self, __value: int) -> tuple[int, int]: ...
    @overload
    def __pow__(self, __x: Literal[0]) -> Literal[1]: ...
    @overload
    def __pow__(self, __value: Literal[0], __mod: None) -> Literal[1]: ...
    @overload
    def __pow__(self, __value: _PositiveInteger, __mod: None = None) -> int: ...
    @overload
    def __pow__(self, __value: _NegativeInteger, __mod: None = None) -> float: ...
    # positive x -> int; negative x -> float
    # return type must be Any as `int | float` causes too many false-positive errors
    @overload
    def __pow__(self, __value: int, __mod: None = None) -> Any: ...
    @overload
    def __pow__(self, __value: int, __mod: int) -> int: ...
    def __rpow__(self, __value: int, __mod: int | None = None) -> Any: ...
    def __and__(self, __value: int) -> int: ...
    def __or__(self, __value: int) -> int: ...
    def __xor__(self, __value: int) -> int: ...
    def __lshift__(self, __value: int) -> int: ...
    def __rshift__(self, __value: int) -> int: ...
    def __rand__(self, __value: int) -> int: ...
    def __ror__(self, __value: int) -> int: ...
    def __rxor__(self, __value: int) -> int: ...
    def __rlshift__(self, __value: int) -> int: ...
    def __rrshift__(self, __value: int) -> int: ...
    def __neg__(self) -> int: ...
    def __pos__(self) -> int: ...
    def __invert__(self) -> int: ...
    def __trunc__(self) -> int: ...
    def __ceil__(self) -> int: ...
    def __floor__(self) -> int: ...
    def __round__(self, __ndigits: SupportsIndex = ...) -> int: ...
    def __getnewargs__(self) -> tuple[int]: ...
    def __eq__(self, __value: object) -> bool: ...
    def __ne__(self, __value: object) -> bool: ...
    def __lt__(self, __value: int) -> bool: ...
    def __le__(self, __value: int) -> bool: ...
    def __gt__(self, __value: int) -> bool: ...
    def __ge__(self, __value: int) -> bool: ...
    def __float__(self) -> float: ...
    def __int__(self) -> int: ...
    def __abs__(self) -> int: ...
    def __hash__(self) -> int: ...
    def __bool__(self) -> bool: ...
    def __index__(self) -> int: ...

class float:
    def __new__(cls, __x: SupportsFloat | SupportsIndex | str | ReadableBuffer = ...) -> Self: ...
    def as_integer_ratio(self) -> tuple[int, int]: ...
    def hex(self) -> str: ...
    def is_integer(self) -> bool: ...
    @classmethod
    def fromhex(cls, __string: str) -> Self: ...
    @property
    def real(self) -> float: ...
    @property
    def imag(self) -> float: ...
    def conjugate(self) -> float: ...
    def __add__(self, __value: float) -> float: ...
    def __sub__(self, __value: float) -> float: ...
    def __mul__(self, __value: float) -> float: ...
    def __floordiv__(self, __value: float) -> float: ...
    def __truediv__(self, __value: float) -> float: ...
    def __mod__(self, __value: float) -> float: ...
    def __divmod__(self, __value: float) -> tuple[float, float]: ...
    @overload
    def __pow__(self, __value: int, __mod: None = None) -> float: ...
    # positive x -> float; negative x -> complex
    # return type must be Any as `float | complex` causes too many false-positive errors
    @overload
    def __pow__(self, __value: float, __mod: None = None) -> Any: ...
    def __radd__(self, __value: float) -> float: ...
    def __rsub__(self, __value: float) -> float: ...
    def __rmul__(self, __value: float) -> float: ...
    def __rfloordiv__(self, __value: float) -> float: ...
    def __rtruediv__(self, __value: float) -> float: ...
    def __rmod__(self, __value: float) -> float: ...
    def __rdivmod__(self, __value: float) -> tuple[float, float]: ...
    @overload
    def __rpow__(self, __value: _PositiveInteger, __mod: None = None) -> float: ...
    @overload
    def __rpow__(self, __value: _NegativeInteger, __mod: None = None) -> complex: ...
    # Returning `complex` for the general case gives too many false-positive errors.
    @overload
    def __rpow__(self, __value: float, __mod: None = None) -> Any: ...
    def __getnewargs__(self) -> tuple[float]: ...
    def __trunc__(self) -> int: ...
    if sys.version_info >= (3, 9):
        def __ceil__(self) -> int: ...
        def __floor__(self) -> int: ...

    @overload
    def __round__(self, __ndigits: None = None) -> int: ...
    @overload
    def __round__(self, __ndigits: SupportsIndex) -> float: ...
    def __eq__(self, __value: object) -> bool: ...
    def __ne__(self, __value: object) -> bool: ...
    def __lt__(self, __value: float) -> bool: ...
    def __le__(self, __value: float) -> bool: ...
    def __gt__(self, __value: float) -> bool: ...
    def __ge__(self, __value: float) -> bool: ...
    def __neg__(self) -> float: ...
    def __pos__(self) -> float: ...
    def __int__(self) -> int: ...
    def __float__(self) -> float: ...
    def __abs__(self) -> float: ...
    def __hash__(self) -> int: ...
    def __bool__(self) -> bool: ...

class complex:
    if sys.version_info >= (3, 8):
        # Python doesn't currently accept SupportsComplex for the second argument
        @overload
        def __new__(
            cls,
            real: complex | SupportsComplex | SupportsFloat | SupportsIndex = ...,
            imag: complex | SupportsFloat | SupportsIndex = ...,
        ) -> Self: ...
        @overload
        def __new__(cls, real: str | SupportsComplex | SupportsFloat | SupportsIndex | complex) -> Self: ...
    else:
        @overload
        def __new__(cls, real: complex | SupportsComplex | SupportsFloat = ..., imag: complex | SupportsFloat = ...) -> Self: ...
        @overload
        def __new__(cls, real: str | SupportsComplex | SupportsFloat | complex) -> Self: ...

    @property
    def real(self) -> float: ...
    @property
    def imag(self) -> float: ...
    def conjugate(self) -> complex: ...
    def __add__(self, __value: complex) -> complex: ...
    def __sub__(self, __value: complex) -> complex: ...
    def __mul__(self, __value: complex) -> complex: ...
    def __pow__(self, __value: complex, __mod: None = None) -> complex: ...
    def __truediv__(self, __value: complex) -> complex: ...
    def __radd__(self, __value: complex) -> complex: ...
    def __rsub__(self, __value: complex) -> complex: ...
    def __rmul__(self, __value: complex) -> complex: ...
    def __rpow__(self, __value: complex, __mod: None = None) -> complex: ...
    def __rtruediv__(self, __value: complex) -> complex: ...
    def __eq__(self, __value: object) -> bool: ...
    def __ne__(self, __value: object) -> bool: ...
    def __neg__(self) -> complex: ...
    def __pos__(self) -> complex: ...
    def __abs__(self) -> float: ...
    def __hash__(self) -> int: ...
    def __bool__(self) -> bool: ...
    if sys.version_info >= (3, 11):
        def __complex__(self) -> complex: ...

class _FormatMapMapping(Protocol):
    def __getitem__(self, __key: str) -> Any: ...

class _TranslateTable(Protocol):
    def __getitem__(self, __key: int) -> str | int | None: ...

class str(Sequence[str]):
    @overload
    def __new__(cls, object: object = ...) -> Self: ...
    @overload
    def __new__(cls, object: ReadableBuffer, encoding: str = ..., errors: str = ...) -> Self: ...
    def capitalize(self) -> str: ...  # type: ignore[misc]
    def casefold(self) -> str: ...  # type: ignore[misc]
    def center(self, __width: SupportsIndex, __fillchar: str = " ") -> str: ...  # type: ignore[misc]
    def count(self, x: str, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...) -> int: ...
    def encode(self, encoding: str = "utf-8", errors: str = "strict") -> bytes: ...
    def endswith(
        self, __suffix: str | tuple[str, ...], __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> bool: ...
    if sys.version_info >= (3, 8):
        def expandtabs(self, tabsize: SupportsIndex = 8) -> str: ...  # type: ignore[misc]
    else:
        def expandtabs(self, tabsize: int = 8) -> str: ...  # type: ignore[misc]

    def find(self, __sub: str, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...) -> int: ...
    def format(self, *args: object, **kwargs: object) -> str: ...
    def format_map(self, map: _FormatMapMapping) -> str: ...
    def index(self, __sub: str, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...) -> int: ...
    def isalnum(self) -> bool: ...
    def isalpha(self) -> bool: ...
    def isascii(self) -> bool: ...
    def isdecimal(self) -> bool: ...
    def isdigit(self) -> bool: ...
    def isidentifier(self) -> bool: ...
    def islower(self) -> bool: ...
    def isnumeric(self) -> bool: ...
    def isprintable(self) -> bool: ...
    def isspace(self) -> bool: ...
    def istitle(self) -> bool: ...
    def isupper(self) -> bool: ...
    def join(self, __iterable: Iterable[str]) -> str: ...  # type: ignore[misc]
    def ljust(self, __width: SupportsIndex, __fillchar: str = " ") -> str: ...  # type: ignore[misc]
    def lower(self) -> str: ...  # type: ignore[misc]
    def lstrip(self, __chars: str | None = None) -> str: ...  # type: ignore[misc]
    def partition(self, __sep: str) -> tuple[str, str, str]: ...  # type: ignore[misc]
    def replace(self, __old: str, __new: str, __count: SupportsIndex = -1) -> str: ...  # type: ignore[misc]
    if sys.version_info >= (3, 9):
        def removeprefix(self, __prefix: str) -> str: ...  # type: ignore[misc]
        def removesuffix(self, __suffix: str) -> str: ...  # type: ignore[misc]

    def rfind(self, __sub: str, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...) -> int: ...
    def rindex(self, __sub: str, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...) -> int: ...
    def rjust(self, __width: SupportsIndex, __fillchar: str = " ") -> str: ...  # type: ignore[misc]
    def rpartition(self, __sep: str) -> tuple[str, str, str]: ...  # type: ignore[misc]
    def rsplit(self, sep: str | None = None, maxsplit: SupportsIndex = -1) -> list[str]: ...  # type: ignore[misc]
    def rstrip(self, __chars: str | None = None) -> str: ...  # type: ignore[misc]
    def split(self, sep: str | None = None, maxsplit: SupportsIndex = -1) -> list[str]: ...  # type: ignore[misc]
    def splitlines(self, keepends: bool = False) -> list[str]: ...  # type: ignore[misc]
    def startswith(
        self, __prefix: str | tuple[str, ...], __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> bool: ...
    def strip(self, __chars: str | None = None) -> str: ...  # type: ignore[misc]
    def swapcase(self) -> str: ...  # type: ignore[misc]
    def title(self) -> str: ...  # type: ignore[misc]
    def translate(self, __table: _TranslateTable) -> str: ...
    def upper(self) -> str: ...  # type: ignore[misc]
    def zfill(self, __width: SupportsIndex) -> str: ...  # type: ignore[misc]
    @staticmethod
    @overload
    def maketrans(__x: dict[int, _T] | dict[str, _T] | dict[str | int, _T]) -> dict[int, _T]: ...
    @staticmethod
    @overload
    def maketrans(__x: str, __y: str) -> dict[int, int]: ...
    @staticmethod
    @overload
    def maketrans(__x: str, __y: str, __z: str) -> dict[int, int | None]: ...
    def __add__(self, __value: str) -> str: ...  # type: ignore[misc]
    # Incompatible with Sequence.__contains__
    def __contains__(self, __key: str) -> bool: ...  # type: ignore[override]
    def __eq__(self, __value: object) -> bool: ...
    def __ge__(self, __value: str) -> bool: ...
    def __getitem__(self, __key: SupportsIndex | slice) -> str: ...
    def __gt__(self, __value: str) -> bool: ...
    def __hash__(self) -> int: ...
    def __iter__(self) -> Iterator[str]: ...  # type: ignore[misc]
    def __le__(self, __value: str) -> bool: ...
    def __len__(self) -> int: ...
    def __lt__(self, __value: str) -> bool: ...
    def __mod__(self, __value: Any) -> str: ...
    def __mul__(self, __value: SupportsIndex) -> str: ...  # type: ignore[misc]
    def __ne__(self, __value: object) -> bool: ...
    def __rmul__(self, __value: SupportsIndex) -> str: ...  # type: ignore[misc]
    def __getnewargs__(self) -> tuple[str]: ...

class bytes(Sequence[int]):
    @overload
    def __new__(cls, __o: Iterable[SupportsIndex] | SupportsIndex | SupportsBytes | ReadableBuffer) -> Self: ...
    @overload
    def __new__(cls, __string: str, encoding: str, errors: str = ...) -> Self: ...
    @overload
    def __new__(cls) -> Self: ...
    def capitalize(self) -> bytes: ...
    def center(self, __width: SupportsIndex, __fillchar: bytes = b" ") -> bytes: ...
    def count(
        self, __sub: ReadableBuffer | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def decode(self, encoding: str = "utf-8", errors: str = "strict") -> str: ...
    def endswith(
        self,
        __suffix: ReadableBuffer | tuple[ReadableBuffer, ...],
        __start: SupportsIndex | None = ...,
        __end: SupportsIndex | None = ...,
    ) -> bool: ...
    if sys.version_info >= (3, 8):
        def expandtabs(self, tabsize: SupportsIndex = 8) -> bytes: ...
    else:
        def expandtabs(self, tabsize: int = ...) -> bytes: ...

    def find(
        self, __sub: ReadableBuffer | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    if sys.version_info >= (3, 8):
        def hex(self, sep: str | bytes = ..., bytes_per_sep: SupportsIndex = ...) -> str: ...
    else:
        def hex(self) -> str: ...

    def index(
        self, __sub: ReadableBuffer | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def isalnum(self) -> bool: ...
    def isalpha(self) -> bool: ...
    def isascii(self) -> bool: ...
    def isdigit(self) -> bool: ...
    def islower(self) -> bool: ...
    def isspace(self) -> bool: ...
    def istitle(self) -> bool: ...
    def isupper(self) -> bool: ...
    def join(self, __iterable_of_bytes: Iterable[ReadableBuffer]) -> bytes: ...
    def ljust(self, __width: SupportsIndex, __fillchar: bytes | bytearray = b" ") -> bytes: ...
    def lower(self) -> bytes: ...
    def lstrip(self, __bytes: ReadableBuffer | None = None) -> bytes: ...
    def partition(self, __sep: ReadableBuffer) -> tuple[bytes, bytes, bytes]: ...
    def replace(self, __old: ReadableBuffer, __new: ReadableBuffer, __count: SupportsIndex = -1) -> bytes: ...
    if sys.version_info >= (3, 9):
        def removeprefix(self, __prefix: ReadableBuffer) -> bytes: ...
        def removesuffix(self, __suffix: ReadableBuffer) -> bytes: ...

    def rfind(
        self, __sub: ReadableBuffer | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def rindex(
        self, __sub: ReadableBuffer | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def rjust(self, __width: SupportsIndex, __fillchar: bytes | bytearray = b" ") -> bytes: ...
    def rpartition(self, __sep: ReadableBuffer) -> tuple[bytes, bytes, bytes]: ...
    def rsplit(self, sep: ReadableBuffer | None = None, maxsplit: SupportsIndex = -1) -> list[bytes]: ...
    def rstrip(self, __bytes: ReadableBuffer | None = None) -> bytes: ...
    def split(self, sep: ReadableBuffer | None = None, maxsplit: SupportsIndex = -1) -> list[bytes]: ...
    def splitlines(self, keepends: bool = False) -> list[bytes]: ...
    def startswith(
        self,
        __prefix: ReadableBuffer | tuple[ReadableBuffer, ...],
        __start: SupportsIndex | None = ...,
        __end: SupportsIndex | None = ...,
    ) -> bool: ...
    def strip(self, __bytes: ReadableBuffer | None = None) -> bytes: ...
    def swapcase(self) -> bytes: ...
    def title(self) -> bytes: ...
    def translate(self, __table: ReadableBuffer | None, delete: bytes = b"") -> bytes: ...
    def upper(self) -> bytes: ...
    def zfill(self, __width: SupportsIndex) -> bytes: ...
    @classmethod
    def fromhex(cls, __string: str) -> Self: ...
    @staticmethod
    def maketrans(__frm: ReadableBuffer, __to: ReadableBuffer) -> bytes: ...
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator[int]: ...
    def __hash__(self) -> int: ...
    @overload
    def __getitem__(self, __key: SupportsIndex) -> int: ...
    @overload
    def __getitem__(self, __key: slice) -> bytes: ...
    def __add__(self, __value: ReadableBuffer) -> bytes: ...
    def __mul__(self, __value: SupportsIndex) -> bytes: ...
    def __rmul__(self, __value: SupportsIndex) -> bytes: ...
    def __mod__(self, __value: Any) -> bytes: ...
    # Incompatible with Sequence.__contains__
    def __contains__(self, __key: SupportsIndex | ReadableBuffer) -> bool: ...  # type: ignore[override]
    def __eq__(self, __value: object) -> bool: ...
    def __ne__(self, __value: object) -> bool: ...
    def __lt__(self, __value: bytes) -> bool: ...
    def __le__(self, __value: bytes) -> bool: ...
    def __gt__(self, __value: bytes) -> bool: ...
    def __ge__(self, __value: bytes) -> bool: ...
    def __getnewargs__(self) -> tuple[bytes]: ...
    if sys.version_info >= (3, 11):
        def __bytes__(self) -> bytes: ...

    def __buffer__(self, __flags: int) -> memoryview: ...

class bytearray(MutableSequence[int]):
    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, __ints: Iterable[SupportsIndex] | SupportsIndex | ReadableBuffer) -> None: ...
    @overload
    def __init__(self, __string: str, encoding: str, errors: str = ...) -> None: ...
    def append(self, __item: SupportsIndex) -> None: ...
    def capitalize(self) -> bytearray: ...
    def center(self, __width: SupportsIndex, __fillchar: bytes = b" ") -> bytearray: ...
    def count(
        self, __sub: ReadableBuffer | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def copy(self) -> bytearray: ...
    def decode(self, encoding: str = "utf-8", errors: str = "strict") -> str: ...
    def endswith(
        self,
        __suffix: ReadableBuffer | tuple[ReadableBuffer, ...],
        __start: SupportsIndex | None = ...,
        __end: SupportsIndex | None = ...,
    ) -> bool: ...
    if sys.version_info >= (3, 8):
        def expandtabs(self, tabsize: SupportsIndex = 8) -> bytearray: ...
    else:
        def expandtabs(self, tabsize: int = ...) -> bytearray: ...

    def extend(self, __iterable_of_ints: Iterable[SupportsIndex]) -> None: ...
    def find(
        self, __sub: ReadableBuffer | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    if sys.version_info >= (3, 8):
        def hex(self, sep: str | bytes = ..., bytes_per_sep: SupportsIndex = ...) -> str: ...
    else:
        def hex(self) -> str: ...

    def index(
        self, __sub: ReadableBuffer | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def insert(self, __index: SupportsIndex, __item: SupportsIndex) -> None: ...
    def isalnum(self) -> bool: ...
    def isalpha(self) -> bool: ...
    def isascii(self) -> bool: ...
    def isdigit(self) -> bool: ...
    def islower(self) -> bool: ...
    def isspace(self) -> bool: ...
    def istitle(self) -> bool: ...
    def isupper(self) -> bool: ...
    def join(self, __iterable_of_bytes: Iterable[ReadableBuffer]) -> bytearray: ...
    def ljust(self, __width: SupportsIndex, __fillchar: bytes | bytearray = b" ") -> bytearray: ...
    def lower(self) -> bytearray: ...
    def lstrip(self, __bytes: ReadableBuffer | None = None) -> bytearray: ...
    def partition(self, __sep: ReadableBuffer) -> tuple[bytearray, bytearray, bytearray]: ...
    def pop(self, __index: int = -1) -> int: ...
    def remove(self, __value: int) -> None: ...
    if sys.version_info >= (3, 9):
        def removeprefix(self, __prefix: ReadableBuffer) -> bytearray: ...
        def removesuffix(self, __suffix: ReadableBuffer) -> bytearray: ...

    def replace(self, __old: ReadableBuffer, __new: ReadableBuffer, __count: SupportsIndex = -1) -> bytearray: ...
    def rfind(
        self, __sub: ReadableBuffer | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def rindex(
        self, __sub: ReadableBuffer | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def rjust(self, __width: SupportsIndex, __fillchar: bytes | bytearray = b" ") -> bytearray: ...
    def rpartition(self, __sep: ReadableBuffer) -> tuple[bytearray, bytearray, bytearray]: ...
    def rsplit(self, sep: ReadableBuffer | None = None, maxsplit: SupportsIndex = -1) -> list[bytearray]: ...
    def rstrip(self, __bytes: ReadableBuffer | None = None) -> bytearray: ...
    def split(self, sep: ReadableBuffer | None = None, maxsplit: SupportsIndex = -1) -> list[bytearray]: ...
    def splitlines(self, keepends: bool = False) -> list[bytearray]: ...
    def startswith(
        self,
        __prefix: ReadableBuffer | tuple[ReadableBuffer, ...],
        __start: SupportsIndex | None = ...,
        __end: SupportsIndex | None = ...,
    ) -> bool: ...
    def strip(self, __bytes: ReadableBuffer | None = None) -> bytearray: ...
    def swapcase(self) -> bytearray: ...
    def title(self) -> bytearray: ...
    def translate(self, __table: ReadableBuffer | None, delete: bytes = b"") -> bytearray: ...
    def upper(self) -> bytearray: ...
    def zfill(self, __width: SupportsIndex) -> bytearray: ...
    @classmethod
    def fromhex(cls, __string: str) -> Self: ...
    @staticmethod
    def maketrans(__frm: ReadableBuffer, __to: ReadableBuffer) -> bytes: ...
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator[int]: ...
    __hash__: ClassVar[None]  # type: ignore[assignment]
    @overload
    def __getitem__(self, __key: SupportsIndex) -> int: ...
    @overload
    def __getitem__(self, __key: slice) -> bytearray: ...
    @overload
    def __setitem__(self, __key: SupportsIndex, __value: SupportsIndex) -> None: ...
    @overload
    def __setitem__(self, __key: slice, __value: Iterable[SupportsIndex] | bytes) -> None: ...
    def __delitem__(self, __key: SupportsIndex | slice) -> None: ...
    def __add__(self, __value: ReadableBuffer) -> bytearray: ...
    # The superclass wants us to accept Iterable[int], but that fails at runtime.
    def __iadd__(self, __value: ReadableBuffer) -> Self: ...  # type: ignore[override]
    def __mul__(self, __value: SupportsIndex) -> bytearray: ...
    def __rmul__(self, __value: SupportsIndex) -> bytearray: ...
    def __imul__(self, __value: SupportsIndex) -> Self: ...
    def __mod__(self, __value: Any) -> bytes: ...
    # Incompatible with Sequence.__contains__
    def __contains__(self, __key: SupportsIndex | ReadableBuffer) -> bool: ...  # type: ignore[override]
    def __eq__(self, __value: object) -> bool: ...
    def __ne__(self, __value: object) -> bool: ...
    def __lt__(self, __value: ReadableBuffer) -> bool: ...
    def __le__(self, __value: ReadableBuffer) -> bool: ...
    def __gt__(self, __value: ReadableBuffer) -> bool: ...
    def __ge__(self, __value: ReadableBuffer) -> bool: ...
    def __alloc__(self) -> int: ...
    def __buffer__(self, __flags: int) -> memoryview: ...
    def __release_buffer__(self, __buffer: memoryview) -> None: ...

@final
class memoryview(Sequence[int]):
    @property
    def format(self) -> str: ...
    @property
    def itemsize(self) -> int: ...
    @property
    def shape(self) -> tuple[int, ...] | None: ...
    @property
    def strides(self) -> tuple[int, ...] | None: ...
    @property
    def suboffsets(self) -> tuple[int, ...] | None: ...
    @property
    def readonly(self) -> bool: ...
    @property
    def ndim(self) -> int: ...
    @property
    def obj(self) -> ReadableBuffer: ...
    @property
    def c_contiguous(self) -> bool: ...
    @property
    def f_contiguous(self) -> bool: ...
    @property
    def contiguous(self) -> bool: ...
    @property
    def nbytes(self) -> int: ...
    def __init__(self, obj: ReadableBuffer) -> None: ...
    def __enter__(self) -> Self: ...
    def __exit__(
        self, __exc_type: type[BaseException] | None, __exc_val: BaseException | None, __exc_tb: TracebackType | None
    ) -> None: ...
    def cast(self, format: str, shape: list[int] | tuple[int, ...] = ...) -> memoryview: ...
    @overload
    def __getitem__(self, __key: SupportsIndex) -> int: ...
    @overload
    def __getitem__(self, __key: slice) -> memoryview: ...
    def __contains__(self, __x: object) -> bool: ...
    def __iter__(self) -> Iterator[int]: ...
    def __len__(self) -> int: ...
    def __eq__(self, __value: object) -> bool: ...
    def __hash__(self) -> int: ...
    @overload
    def __setitem__(self, __key: slice, __value: ReadableBuffer) -> None: ...
    @overload
    def __setitem__(self, __key: SupportsIndex, __value: SupportsIndex) -> None: ...
    if sys.version_info >= (3, 10):
        def tobytes(self, order: Literal["C", "F", "A"] | None = "C") -> bytes: ...
    elif sys.version_info >= (3, 8):
        def tobytes(self, order: Literal["C", "F", "A"] | None = None) -> bytes: ...
    else:
        def tobytes(self) -> bytes: ...

    def tolist(self) -> list[int]: ...
    if sys.version_info >= (3, 8):
        def toreadonly(self) -> memoryview: ...

    def release(self) -> None: ...
    if sys.version_info >= (3, 8):
        def hex(self, sep: str | bytes = ..., bytes_per_sep: SupportsIndex = ...) -> str: ...
    else:
        def hex(self) -> str: ...

    def __buffer__(self, __flags: int) -> memoryview: ...
    def __release_buffer__(self, __buffer: memoryview) -> None: ...

@final
class bool(int):
    def __new__(cls, __o: object = ...) -> Self: ...
    # The following overloads could be represented more elegantly with a TypeVar("_B", bool, int),
    # however mypy has a bug regarding TypeVar constraints (https://github.com/python/mypy/issues/11880).
    @overload
    def __and__(self, __value: bool) -> bool: ...
    @overload
    def __and__(self, __value: int) -> int: ...
    @overload
    def __or__(self, __value: bool) -> bool: ...
    @overload
    def __or__(self, __value: int) -> int: ...
    @overload
    def __xor__(self, __value: bool) -> bool: ...
    @overload
    def __xor__(self, __value: int) -> int: ...
    @overload
    def __rand__(self, __value: bool) -> bool: ...
    @overload
    def __rand__(self, __value: int) -> int: ...
    @overload
    def __ror__(self, __value: bool) -> bool: ...
    @overload
    def __ror__(self, __value: int) -> int: ...
    @overload
    def __rxor__(self, __value: bool) -> bool: ...
    @overload
    def __rxor__(self, __value: int) -> int: ...
    def __getnewargs__(self) -> tuple[int]: ...

@final
class slice:
    @property
    def start(self) -> Any: ...
    @property
    def step(self) -> Any: ...
    @property
    def stop(self) -> Any: ...
    @overload
    def __init__(self, __stop: Any) -> None: ...
    @overload
    def __init__(self, __start: Any, __stop: Any, __step: Any = ...) -> None: ...
    def __eq__(self, __value: object) -> bool: ...
    __hash__: ClassVar[None]  # type: ignore[assignment]
    def indices(self, __len: SupportsIndex) -> tuple[int, int, int]: ...

class tuple(Sequence[_T_co], Generic[_T_co]):
    def __new__(cls, __iterable: Iterable[_T_co] = ...) -> Self: ...
    def __len__(self) -> int: ...
    def __contains__(self, __key: object) -> bool: ...
    @overload
    def __getitem__(self, __key: SupportsIndex) -> _T_co: ...
    @overload
    def __getitem__(self, __key: slice) -> tuple[_T_co, ...]: ...
    def __iter__(self) -> Iterator[_T_co]: ...
    def __lt__(self, __value: tuple[_T_co, ...]) -> bool: ...
    def __le__(self, __value: tuple[_T_co, ...]) -> bool: ...
    def __gt__(self, __value: tuple[_T_co, ...]) -> bool: ...
    def __ge__(self, __value: tuple[_T_co, ...]) -> bool: ...
    def __eq__(self, __value: object) -> bool: ...
    def __hash__(self) -> int: ...
    @overload
    def __add__(self, __value: tuple[_T_co, ...]) -> tuple[_T_co, ...]: ...
    @overload
    def __add__(self, __value: tuple[_T, ...]) -> tuple[_T_co | _T, ...]: ...
    def __mul__(self, __value: SupportsIndex) -> tuple[_T_co, ...]: ...
    def __rmul__(self, __value: SupportsIndex) -> tuple[_T_co, ...]: ...
    def count(self, __value: Any) -> int: ...
    def index(self, __value: Any, __start: SupportsIndex = 0, __stop: SupportsIndex = sys.maxsize) -> int: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...

# Doesn't exist at runtime, but deleting this breaks mypy. See #2999
@final
@type_check_only
class function:
    # Make sure this class definition stays roughly in line with `types.FunctionType`
    @property
    def __closure__(self) -> tuple[_Cell, ...] | None: ...
    __code__: CodeType
    __defaults__: tuple[Any, ...] | None
    __dict__: dict[str, Any]
    @property
    def __globals__(self) -> dict[str, Any]: ...
    __name__: str
    __qualname__: str
    __annotations__: dict[str, Any]
    __kwdefaults__: dict[str, Any]
    if sys.version_info >= (3, 10):
        @property
        def __builtins__(self) -> dict[str, Any]: ...
    if sys.version_info >= (3, 12):
        __type_params__: tuple[TypeVar | ParamSpec | TypeVarTuple, ...]

    __module__: str
    # mypy uses `builtins.function.__get__` to represent methods, properties, and getset_descriptors so we type the return as Any.
    def __get__(self, __instance: object, __owner: type | None = None) -> Any: ...

class list(MutableSequence[_T], Generic[_T]):
    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, __iterable: Iterable[_T]) -> None: ...
    def copy(self) -> list[_T]: ...
    def append(self, __object: _T) -> None: ...
    def extend(self, __iterable: Iterable[_T]) -> None: ...
    def pop(self, __index: SupportsIndex = -1) -> _T: ...
    # Signature of `list.index` should be kept in line with `collections.UserList.index()`
    # and multiprocessing.managers.ListProxy.index()
    def index(self, __value: _T, __start: SupportsIndex = 0, __stop: SupportsIndex = sys.maxsize) -> int: ...
    def count(self, __value: _T) -> int: ...
    def insert(self, __index: SupportsIndex, __object: _T) -> None: ...
    def remove(self, __value: _T) -> None: ...
    # Signature of `list.sort` should be kept inline with `collections.UserList.sort()`
    # and multiprocessing.managers.ListProxy.sort()
    #
    # Use list[SupportsRichComparisonT] for the first overload rather than [SupportsRichComparison]
    # to work around invariance
    @overload
    def sort(self: list[SupportsRichComparisonT], *, key: None = None, reverse: bool = False) -> None: ...
    @overload
    def sort(self, *, key: Callable[[_T], SupportsRichComparison], reverse: bool = False) -> None: ...
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator[_T]: ...
    __hash__: ClassVar[None]  # type: ignore[assignment]
    @overload
    def __getitem__(self, __i: SupportsIndex) -> _T: ...
    @overload
    def __getitem__(self, __s: slice) -> list[_T]: ...
    @overload
    def __setitem__(self, __key: SupportsIndex, __value: _T) -> None: ...
    @overload
    def __setitem__(self, __key: slice, __value: Iterable[_T]) -> None: ...
    def __delitem__(self, __key: SupportsIndex | slice) -> None: ...
    # Overloading looks unnecessary, but is needed to work around complex mypy problems
    @overload
    def __add__(self, __value: list[_T]) -> list[_T]: ...
    @overload
    def __add__(self, __value: list[_S]) -> list[_S | _T]: ...
    def __iadd__(self, __value: Iterable[_T]) -> Self: ...  # type: ignore[misc]
    def __mul__(self, __value: SupportsIndex) -> list[_T]: ...
    def __rmul__(self, __value: SupportsIndex) -> list[_T]: ...
    def __imul__(self, __value: SupportsIndex) -> Self: ...
    def __contains__(self, __key: object) -> bool: ...
    def __reversed__(self) -> Iterator[_T]: ...
    def __gt__(self, __value: list[_T]) -> bool: ...
    def __ge__(self, __value: list[_T]) -> bool: ...
    def __lt__(self, __value: list[_T]) -> bool: ...
    def __le__(self, __value: list[_T]) -> bool: ...
    def __eq__(self, __value: object) -> bool: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...

class dict(MutableMapping[_KT, _VT], Generic[_KT, _VT]):
    # __init__ should be kept roughly in line with `collections.UserDict.__init__`, which has similar semantics
    # Also multiprocessing.managers.SyncManager.dict()
    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self: dict[str, _VT], **kwargs: _VT) -> None: ...
    @overload
    def __init__(self, __map: SupportsKeysAndGetItem[_KT, _VT]) -> None: ...
    @overload
    def __init__(self: dict[str, _VT], __map: SupportsKeysAndGetItem[str, _VT], **kwargs: _VT) -> None: ...
    @overload
    def __init__(self, __iterable: Iterable[tuple[_KT, _VT]]) -> None: ...
    @overload
    def __init__(self: dict[str, _VT], __iterable: Iterable[tuple[str, _VT]], **kwargs: _VT) -> None: ...
    # Next two overloads are for dict(string.split(sep) for string in iterable)
    # Cannot be Iterable[Sequence[_T]] or otherwise dict(["foo", "bar", "baz"]) is not an error
    @overload
    def __init__(self: dict[str, str], __iterable: Iterable[list[str]]) -> None: ...
    @overload
    def __init__(self: dict[bytes, bytes], __iterable: Iterable[list[bytes]]) -> None: ...
    def __new__(cls, *args: Any, **kwargs: Any) -> Self: ...
    def copy(self) -> dict[_KT, _VT]: ...
    def keys(self) -> dict_keys[_KT, _VT]: ...
    def values(self) -> dict_values[_KT, _VT]: ...
    def items(self) -> dict_items[_KT, _VT]: ...
    # Signature of `dict.fromkeys` should be kept identical to `fromkeys` methods of `OrderedDict`/`ChainMap`/`UserDict` in `collections`
    # TODO: the true signature of `dict.fromkeys` is not expressible in the current type system.
    # See #3800 & https://github.com/python/typing/issues/548#issuecomment-683336963.
    @classmethod
    @overload
    def fromkeys(cls, __iterable: Iterable[_T], __value: None = None) -> dict[_T, Any | None]: ...
    @classmethod
    @overload
    def fromkeys(cls, __iterable: Iterable[_T], __value: _S) -> dict[_T, _S]: ...
    # Positional-only in dict, but not in MutableMapping
    @overload  # type: ignore[override]
    def get(self, __key: _KT) -> _VT | None: ...
    @overload
    def get(self, __key: _KT, __default: _VT) -> _VT: ...
    @overload
    def get(self, __key: _KT, __default: _T) -> _VT | _T: ...
    @overload
    def pop(self, __key: _KT) -> _VT: ...
    @overload
    def pop(self, __key: _KT, __default: _VT) -> _VT: ...
    @overload
    def pop(self, __key: _KT, __default: _T) -> _VT | _T: ...
    def __len__(self) -> int: ...
    def __getitem__(self, __key: _KT) -> _VT: ...
    def __setitem__(self, __key: _KT, __value: _VT) -> None: ...
    def __delitem__(self, __key: _KT) -> None: ...
    def __iter__(self) -> Iterator[_KT]: ...
    def __eq__(self, __value: object) -> bool: ...
    if sys.version_info >= (3, 8):
        def __reversed__(self) -> Iterator[_KT]: ...
    __hash__: ClassVar[None]  # type: ignore[assignment]
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...
        @overload
        def __or__(self, __value: Mapping[_KT, _VT]) -> dict[_KT, _VT]: ...
        @overload
        def __or__(self, __value: Mapping[_T1, _T2]) -> dict[_KT | _T1, _VT | _T2]: ...
        @overload
        def __ror__(self, __value: Mapping[_KT, _VT]) -> dict[_KT, _VT]: ...
        @overload
        def __ror__(self, __value: Mapping[_T1, _T2]) -> dict[_KT | _T1, _VT | _T2]: ...
        # dict.__ior__ should be kept roughly in line with MutableMapping.update()
        @overload  # type: ignore[misc]
        def __ior__(self, __value: SupportsKeysAndGetItem[_KT, _VT]) -> Self: ...
        @overload
        def __ior__(self, __value: Iterable[tuple[_KT, _VT]]) -> Self: ...

class set(MutableSet[_T], Generic[_T]):
    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, __iterable: Iterable[_T]) -> None: ...
    def add(self, __element: _T) -> None: ...
    def copy(self) -> set[_T]: ...
    def difference(self, *s: Iterable[Any]) -> set[_T]: ...
    def difference_update(self, *s: Iterable[Any]) -> None: ...
    def discard(self, __element: _T) -> None: ...
    def intersection(self, *s: Iterable[Any]) -> set[_T]: ...
    def intersection_update(self, *s: Iterable[Any]) -> None: ...
    def isdisjoint(self, __s: Iterable[Any]) -> bool: ...
    def issubset(self, __s: Iterable[Any]) -> bool: ...
    def issuperset(self, __s: Iterable[Any]) -> bool: ...
    def remove(self, __element: _T) -> None: ...
    def symmetric_difference(self, __s: Iterable[_T]) -> set[_T]: ...
    def symmetric_difference_update(self, __s: Iterable[_T]) -> None: ...
    def union(self, *s: Iterable[_S]) -> set[_T | _S]: ...
    def update(self, *s: Iterable[_T]) -> None: ...
    def __len__(self) -> int: ...
    def __contains__(self, __o: object) -> bool: ...
    def __iter__(self) -> Iterator[_T]: ...
    def __and__(self, __value: AbstractSet[object]) -> set[_T]: ...
    def __iand__(self, __value: AbstractSet[object]) -> Self: ...
    def __or__(self, __value: AbstractSet[_S]) -> set[_T | _S]: ...
    def __ior__(self, __value: AbstractSet[_T]) -> Self: ...  # type: ignore[override,misc]
    def __sub__(self, __value: AbstractSet[_T | None]) -> set[_T]: ...
    def __isub__(self, __value: AbstractSet[object]) -> Self: ...
    def __xor__(self, __value: AbstractSet[_S]) -> set[_T | _S]: ...
    def __ixor__(self, __value: AbstractSet[_T]) -> Self: ...  # type: ignore[override,misc]
    def __le__(self, __value: AbstractSet[object]) -> bool: ...
    def __lt__(self, __value: AbstractSet[object]) -> bool: ...
    def __ge__(self, __value: AbstractSet[object]) -> bool: ...
    def __gt__(self, __value: AbstractSet[object]) -> bool: ...
    def __eq__(self, __value: object) -> bool: ...
    __hash__: ClassVar[None]  # type: ignore[assignment]
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...

class frozenset(AbstractSet[_T_co], Generic[_T_co]):
    @overload
    def __new__(cls) -> Self: ...
    @overload
    def __new__(cls, __iterable: Iterable[_T_co]) -> Self: ...
    def copy(self) -> frozenset[_T_co]: ...
    def difference(self, *s: Iterable[object]) -> frozenset[_T_co]: ...
    def intersection(self, *s: Iterable[object]) -> frozenset[_T_co]: ...
    def isdisjoint(self, __s: Iterable[_T_co]) -> bool: ...
    def issubset(self, __s: Iterable[object]) -> bool: ...
    def issuperset(self, __s: Iterable[object]) -> bool: ...
    def symmetric_difference(self, __s: Iterable[_T_co]) -> frozenset[_T_co]: ...
    def union(self, *s: Iterable[_S]) -> frozenset[_T_co | _S]: ...
    def __len__(self) -> int: ...
    def __contains__(self, __o: object) -> bool: ...
    def __iter__(self) -> Iterator[_T_co]: ...
    def __and__(self, __value: AbstractSet[_T_co]) -> frozenset[_T_co]: ...
    def __or__(self, __value: AbstractSet[_S]) -> frozenset[_T_co | _S]: ...
    def __sub__(self, __value: AbstractSet[_T_co]) -> frozenset[_T_co]: ...
    def __xor__(self, __value: AbstractSet[_S]) -> frozenset[_T_co | _S]: ...
    def __le__(self, __value: AbstractSet[object]) -> bool: ...
    def __lt__(self, __value: AbstractSet[object]) -> bool: ...
    def __ge__(self, __value: AbstractSet[object]) -> bool: ...
    def __gt__(self, __value: AbstractSet[object]) -> bool: ...
    def __eq__(self, __value: object) -> bool: ...
    def __hash__(self) -> int: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...

class enumerate(Iterator[tuple[int, _T]], Generic[_T]):
    def __init__(self, iterable: Iterable[_T], start: int = ...) -> None: ...
    def __iter__(self) -> Self: ...
    def __next__(self) -> tuple[int, _T]: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...

@final
class range(Sequence[int]):
    @property
    def start(self) -> int: ...
    @property
    def stop(self) -> int: ...
    @property
    def step(self) -> int: ...
    @overload
    def __init__(self, __stop: SupportsIndex) -> None: ...
    @overload
    def __init__(self, __start: SupportsIndex, __stop: SupportsIndex, __step: SupportsIndex = ...) -> None: ...
    def count(self, __value: int) -> int: ...
    def index(self, __value: int) -> int: ...  # type: ignore[override]
    def __len__(self) -> int: ...
    def __eq__(self, __value: object) -> bool: ...
    def __hash__(self) -> int: ...
    def __contains__(self, __key: object) -> bool: ...
    def __iter__(self) -> Iterator[int]: ...
    @overload
    def __getitem__(self, __key: SupportsIndex) -> int: ...
    @overload
    def __getitem__(self, __key: slice) -> range: ...
    def __reversed__(self) -> Iterator[int]: ...

class property:
    fget: Callable[[Any], Any] | None
    fset: Callable[[Any, Any], None] | None
    fdel: Callable[[Any], None] | None
    __isabstractmethod__: bool
    def __init__(
        self,
        fget: Callable[[Any], Any] | None = ...,
        fset: Callable[[Any, Any], None] | None = ...,
        fdel: Callable[[Any], None] | None = ...,
        doc: str | None = ...,
    ) -> None: ...
    def getter(self, __fget: Callable[[Any], Any]) -> property: ...
    def setter(self, __fset: Callable[[Any, Any], None]) -> property: ...
    def deleter(self, __fdel: Callable[[Any], None]) -> property: ...
    def __get__(self, __instance: Any, __owner: type | None = None) -> Any: ...
    def __set__(self, __instance: Any, __value: Any) -> None: ...
    def __delete__(self, __instance: Any) -> None: ...

@final
class _NotImplementedType(Any):
    # A little weird, but typing the __call__ as NotImplemented makes the error message
    # for NotImplemented() much better
    __call__: NotImplemented  # type: ignore[valid-type]  # pyright: ignore[reportGeneralTypeIssues]

NotImplemented: _NotImplementedType

def abs(__x: SupportsAbs[_T]) -> _T: ...
def all(__iterable: Iterable[object]) -> bool: ...
def any(__iterable: Iterable[object]) -> bool: ...
def ascii(__obj: object) -> str: ...
def bin(__number: int | SupportsIndex) -> str: ...
def breakpoint(*args: Any, **kws: Any) -> None: ...
def callable(__obj: object) -> TypeGuard[Callable[..., object]]: ...
def chr(__i: int) -> str: ...

# We define this here instead of using os.PathLike to avoid import cycle issues.
# See https://github.com/python/typeshed/pull/991#issuecomment-288160993
class _PathLike(Protocol[AnyStr_co]):
    def __fspath__(self) -> AnyStr_co: ...

if sys.version_info >= (3, 10):
    def aiter(__async_iterable: SupportsAiter[_SupportsAnextT]) -> _SupportsAnextT: ...

    class _SupportsSynchronousAnext(Protocol[_AwaitableT_co]):
        def __anext__(self) -> _AwaitableT_co: ...

    @overload
    # `anext` is not, in fact, an async function. When default is not provided
    # `anext` is just a passthrough for `obj.__anext__`
    # See discussion in #7491 and pure-Python implementation of `anext` at https://github.com/python/cpython/blob/ea786a882b9ed4261eafabad6011bc7ef3b5bf94/Lib/test/test_asyncgen.py#L52-L80
    def anext(__i: _SupportsSynchronousAnext[_AwaitableT]) -> _AwaitableT: ...
    @overload
    async def anext(__i: SupportsAnext[_T], default: _VT) -> _T | _VT: ...

# compile() returns a CodeType, unless the flags argument includes PyCF_ONLY_AST (=1024),
# in which case it returns ast.AST. We have overloads for flag 0 (the default) and for
# explicitly passing PyCF_ONLY_AST. We fall back to Any for other values of flags.
if sys.version_info >= (3, 8):
    @overload
    def compile(
        source: str | ReadableBuffer | _ast.Module | _ast.Expression | _ast.Interactive,
        filename: str | ReadableBuffer | _PathLike[Any],
        mode: str,
        flags: Literal[0],
        dont_inherit: bool = False,
        optimize: int = -1,
        *,
        _feature_version: int = -1,
    ) -> CodeType: ...
    @overload
    def compile(
        source: str | ReadableBuffer | _ast.Module | _ast.Expression | _ast.Interactive,
        filename: str | ReadableBuffer | _PathLike[Any],
        mode: str,
        *,
        dont_inherit: bool = False,
        optimize: int = -1,
        _feature_version: int = -1,
    ) -> CodeType: ...
    @overload
    def compile(
        source: str | ReadableBuffer | _ast.Module | _ast.Expression | _ast.Interactive,
        filename: str | ReadableBuffer | _PathLike[Any],
        mode: str,
        flags: Literal[1024],
        dont_inherit: bool = False,
        optimize: int = -1,
        *,
        _feature_version: int = -1,
    ) -> _ast.AST: ...
    @overload
    def compile(
        source: str | ReadableBuffer | _ast.Module | _ast.Expression | _ast.Interactive,
        filename: str | ReadableBuffer | _PathLike[Any],
        mode: str,
        flags: int,
        dont_inherit: bool = False,
        optimize: int = -1,
        *,
        _feature_version: int = -1,
    ) -> Any: ...

else:
    @overload
    def compile(
        source: str | ReadableBuffer | _ast.Module | _ast.Expression | _ast.Interactive,
        filename: str | ReadableBuffer | _PathLike[Any],
        mode: str,
        flags: Literal[0],
        dont_inherit: bool = False,
        optimize: int = -1,
    ) -> CodeType: ...
    @overload
    def compile(
        source: str | ReadableBuffer | _ast.Module | _ast.Expression | _ast.Interactive,
        filename: str | ReadableBuffer | _PathLike[Any],
        mode: str,
        *,
        dont_inherit: bool = False,
        optimize: int = -1,
    ) -> CodeType: ...
    @overload
    def compile(
        source: str | ReadableBuffer | _ast.Module | _ast.Expression | _ast.Interactive,
        filename: str | ReadableBuffer | _PathLike[Any],
        mode: str,
        flags: Literal[1024],
        dont_inherit: bool = False,
        optimize: int = -1,
    ) -> _ast.AST: ...
    @overload
    def compile(
        source: str | ReadableBuffer | _ast.Module | _ast.Expression | _ast.Interactive,
        filename: str | ReadableBuffer | _PathLike[Any],
        mode: str,
        flags: int,
        dont_inherit: bool = False,
        optimize: int = -1,
    ) -> Any: ...

def copyright() -> None: ...
def credits() -> None: ...
def delattr(__obj: object, __name: str) -> None: ...
def dir(__o: object = ...) -> list[str]: ...
@overload
def divmod(__x: SupportsDivMod[_T_contra, _T_co], __y: _T_contra) -> _T_co: ...
@overload
def divmod(__x: _T_contra, __y: SupportsRDivMod[_T_contra, _T_co]) -> _T_co: ...

# The `globals` argument to `eval` has to be `dict[str, Any]` rather than `dict[str, object]` due to invariance.
# (The `globals` argument has to be a "real dict", rather than any old mapping, unlike the `locals` argument.)
def eval(
    __source: str | ReadableBuffer | CodeType,
    __globals: dict[str, Any] | None = None,
    __locals: Mapping[str, object] | None = None,
) -> Any: ...

# Comment above regarding `eval` applies to `exec` as well
if sys.version_info >= (3, 11):
    def exec(
        __source: str | ReadableBuffer | CodeType,
        __globals: dict[str, Any] | None = None,
        __locals: Mapping[str, object] | None = None,
        *,
        closure: tuple[_Cell, ...] | None = None,
    ) -> None: ...

else:
    def exec(
        __source: str | ReadableBuffer | CodeType,
        __globals: dict[str, Any] | None = None,
        __locals: Mapping[str, object] | None = None,
    ) -> None: ...

def exit(code: sys._ExitCode = None) -> NoReturn: ...

class filter(Iterator[_T], Generic[_T]):
    @overload
    def __init__(self, __function: None, __iterable: Iterable[_T | None]) -> None: ...
    @overload
    def __init__(self, __function: Callable[[_S], TypeGuard[_T]], __iterable: Iterable[_S]) -> None: ...
    @overload
    def __init__(self, __function: Callable[[_T], Any], __iterable: Iterable[_T]) -> None: ...
    def __iter__(self) -> Self: ...
    def __next__(self) -> _T: ...

def format(__value: object, __format_spec: str = "") -> str: ...
@overload
def getattr(__o: object, __name: str) -> Any: ...

# While technically covered by the last overload, spelling out the types for None, bool
# and basic containers help mypy out in some tricky situations involving type context
# (aka bidirectional inference)
@overload
def getattr(__o: object, __name: str, __default: None) -> Any | None: ...
@overload
def getattr(__o: object, __name: str, __default: bool) -> Any | bool: ...
@overload
def getattr(__o: object, name: str, __default: list[Any]) -> Any | list[Any]: ...
@overload
def getattr(__o: object, name: str, __default: dict[Any, Any]) -> Any | dict[Any, Any]: ...
@overload
def getattr(__o: object, __name: str, __default: _T) -> Any | _T: ...
def globals() -> dict[str, Any]: ...
def hasattr(__obj: object, __name: str) -> bool: ...
def hash(__obj: object) -> int: ...
def help(request: object = ...) -> None: ...
def hex(__number: int | SupportsIndex) -> str: ...
def id(__obj: object) -> int: ...
def input(__prompt: object = "") -> str: ...

class _GetItemIterable(Protocol[_T_co]):
    def __getitem__(self, __i: int) -> _T_co: ...

@overload
def iter(__iterable: SupportsIter[_SupportsNextT]) -> _SupportsNextT: ...
@overload
def iter(__iterable: _GetItemIterable[_T]) -> Iterator[_T]: ...
@overload
def iter(__function: Callable[[], _T | None], __sentinel: None) -> Iterator[_T]: ...
@overload
def iter(__function: Callable[[], _T], __sentinel: object) -> Iterator[_T]: ...

# Keep this alias in sync with unittest.case._ClassInfo
if sys.version_info >= (3, 10):
    _ClassInfo: TypeAlias = type | types.UnionType | tuple[_ClassInfo, ...]
else:
    _ClassInfo: TypeAlias = type | tuple[_ClassInfo, ...]

def isinstance(__obj: object, __class_or_tuple: _ClassInfo) -> bool: ...
def issubclass(__cls: type, __class_or_tuple: _ClassInfo) -> bool: ...
def len(__obj: Sized) -> int: ...
def license() -> None: ...
def locals() -> dict[str, Any]: ...

class map(Iterator[_S], Generic[_S]):
    @overload
    def __init__(self, __func: Callable[[_T1], _S], __iter1: Iterable[_T1]) -> None: ...
    @overload
    def __init__(self, __func: Callable[[_T1, _T2], _S], __iter1: Iterable[_T1], __iter2: Iterable[_T2]) -> None: ...
    @overload
    def __init__(
        self, __func: Callable[[_T1, _T2, _T3], _S], __iter1: Iterable[_T1], __iter2: Iterable[_T2], __iter3: Iterable[_T3]
    ) -> None: ...
    @overload
    def __init__(
        self,
        __func: Callable[[_T1, _T2, _T3, _T4], _S],
        __iter1: Iterable[_T1],
        __iter2: Iterable[_T2],
        __iter3: Iterable[_T3],
        __iter4: Iterable[_T4],
    ) -> None: ...
    @overload
    def __init__(
        self,
        __func: Callable[[_T1, _T2, _T3, _T4, _T5], _S],
        __iter1: Iterable[_T1],
        __iter2: Iterable[_T2],
        __iter3: Iterable[_T3],
        __iter4: Iterable[_T4],
        __iter5: Iterable[_T5],
    ) -> None: ...
    @overload
    def __init__(
        self,
        __func: Callable[..., _S],
        __iter1: Iterable[Any],
        __iter2: Iterable[Any],
        __iter3: Iterable[Any],
        __iter4: Iterable[Any],
        __iter5: Iterable[Any],
        __iter6: Iterable[Any],
        *iterables: Iterable[Any],
    ) -> None: ...
    def __iter__(self) -> Self: ...
    def __next__(self) -> _S: ...

@overload
def max(
    __arg1: SupportsRichComparisonT, __arg2: SupportsRichComparisonT, *_args: SupportsRichComparisonT, key: None = None
) -> SupportsRichComparisonT: ...
@overload
def max(__arg1: _T, __arg2: _T, *_args: _T, key: Callable[[_T], SupportsRichComparison]) -> _T: ...
@overload
def max(__iterable: Iterable[SupportsRichComparisonT], *, key: None = None) -> SupportsRichComparisonT: ...
@overload
def max(__iterable: Iterable[_T], *, key: Callable[[_T], SupportsRichComparison]) -> _T: ...
@overload
def max(__iterable: Iterable[SupportsRichComparisonT], *, key: None = None, default: _T) -> SupportsRichComparisonT | _T: ...
@overload
def max(__iterable: Iterable[_T1], *, key: Callable[[_T1], SupportsRichComparison], default: _T2) -> _T1 | _T2: ...
@overload
def min(
    __arg1: SupportsRichComparisonT, __arg2: SupportsRichComparisonT, *_args: SupportsRichComparisonT, key: None = None
) -> SupportsRichComparisonT: ...
@overload
def min(__arg1: _T, __arg2: _T, *_args: _T, key: Callable[[_T], SupportsRichComparison]) -> _T: ...
@overload
def min(__iterable: Iterable[SupportsRichComparisonT], *, key: None = None) -> SupportsRichComparisonT: ...
@overload
def min(__iterable: Iterable[_T], *, key: Callable[[_T], SupportsRichComparison]) -> _T: ...
@overload
def min(__iterable: Iterable[SupportsRichComparisonT], *, key: None = None, default: _T) -> SupportsRichComparisonT | _T: ...
@overload
def min(__iterable: Iterable[_T1], *, key: Callable[[_T1], SupportsRichComparison], default: _T2) -> _T1 | _T2: ...
@overload
def next(__i: SupportsNext[_T]) -> _T: ...
@overload
def next(__i: SupportsNext[_T], __default: _VT) -> _T | _VT: ...
def oct(__number: int | SupportsIndex) -> str: ...

_Opener: TypeAlias = Callable[[str, int], int]

# Text mode: always returns a TextIOWrapper
@overload
def open(
    file: FileDescriptorOrPath,
    mode: OpenTextMode = "r",
    buffering: int = -1,
    encoding: str | None = None,
    errors: str | None = None,
    newline: str | None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> TextIOWrapper: ...

# Unbuffered binary mode: returns a FileIO
@overload
def open(
    file: FileDescriptorOrPath,
    mode: OpenBinaryMode,
    buffering: Literal[0],
    encoding: None = None,
    errors: None = None,
    newline: None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> FileIO: ...

# Buffering is on: return BufferedRandom, BufferedReader, or BufferedWriter
@overload
def open(
    file: FileDescriptorOrPath,
    mode: OpenBinaryModeUpdating,
    buffering: Literal[-1, 1] = -1,
    encoding: None = None,
    errors: None = None,
    newline: None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> BufferedRandom: ...
@overload
def open(
    file: FileDescriptorOrPath,
    mode: OpenBinaryModeWriting,
    buffering: Literal[-1, 1] = -1,
    encoding: None = None,
    errors: None = None,
    newline: None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> BufferedWriter: ...
@overload
def open(
    file: FileDescriptorOrPath,
    mode: OpenBinaryModeReading,
    buffering: Literal[-1, 1] = -1,
    encoding: None = None,
    errors: None = None,
    newline: None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> BufferedReader: ...

# Buffering cannot be determined: fall back to BinaryIO
@overload
def open(
    file: FileDescriptorOrPath,
    mode: OpenBinaryMode,
    buffering: int = -1,
    encoding: None = None,
    errors: None = None,
    newline: None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> BinaryIO: ...

# Fallback if mode is not specified
@overload
def open(
    file: FileDescriptorOrPath,
    mode: str,
    buffering: int = -1,
    encoding: str | None = None,
    errors: str | None = None,
    newline: str | None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> IO[Any]: ...
def ord(__c: str | bytes | bytearray) -> int: ...

class _SupportsWriteAndFlush(SupportsWrite[_T_contra], Protocol[_T_contra]):
    def flush(self) -> None: ...

@overload
def print(
    *values: object,
    sep: str | None = " ",
    end: str | None = "\n",
    file: SupportsWrite[str] | None = None,
    flush: Literal[False] = False,
) -> None: ...
@overload
def print(
    *values: object, sep: str | None = " ", end: str | None = "\n", file: _SupportsWriteAndFlush[str] | None = None, flush: bool
) -> None: ...

_E = TypeVar("_E", contravariant=True)
_M = TypeVar("_M", contravariant=True)

class _SupportsPow2(Protocol[_E, _T_co]):
    def __pow__(self, __other: _E) -> _T_co: ...

class _SupportsPow3NoneOnly(Protocol[_E, _T_co]):
    def __pow__(self, __other: _E, __modulo: None = None) -> _T_co: ...

class _SupportsPow3(Protocol[_E, _M, _T_co]):
    def __pow__(self, __other: _E, __modulo: _M) -> _T_co: ...

_SupportsSomeKindOfPow = (  # noqa: Y026  # TODO: Use TypeAlias once mypy bugs are fixed
    _SupportsPow2[Any, Any] | _SupportsPow3NoneOnly[Any, Any] | _SupportsPow3[Any, Any, Any]
)

if sys.version_info >= (3, 8):
    # TODO: `pow(int, int, Literal[0])` fails at runtime,
    # but adding a `NoReturn` overload isn't a good solution for expressing that (see #8566).
    @overload
    def pow(base: int, exp: int, mod: int) -> int: ...
    @overload
    def pow(base: int, exp: Literal[0], mod: None = None) -> Literal[1]: ...
    @overload
    def pow(base: int, exp: _PositiveInteger, mod: None = None) -> int: ...
    @overload
    def pow(base: int, exp: _NegativeInteger, mod: None = None) -> float: ...
    # int base & positive-int exp -> int; int base & negative-int exp -> float
    # return type must be Any as `int | float` causes too many false-positive errors
    @overload
    def pow(base: int, exp: int, mod: None = None) -> Any: ...
    @overload
    def pow(base: _PositiveInteger, exp: float, mod: None = None) -> float: ...
    @overload
    def pow(base: _NegativeInteger, exp: float, mod: None = None) -> complex: ...
    @overload
    def pow(base: float, exp: int, mod: None = None) -> float: ...
    # float base & float exp could return float or complex
    # return type must be Any (same as complex base, complex exp),
    # as `float | complex` causes too many false-positive errors
    @overload
    def pow(base: float, exp: complex | _SupportsSomeKindOfPow, mod: None = None) -> Any: ...
    @overload
    def pow(base: complex, exp: complex | _SupportsSomeKindOfPow, mod: None = None) -> complex: ...
    @overload
    def pow(base: _SupportsPow2[_E, _T_co], exp: _E, mod: None = None) -> _T_co: ...
    @overload
    def pow(base: _SupportsPow3NoneOnly[_E, _T_co], exp: _E, mod: None = None) -> _T_co: ...
    @overload
    def pow(base: _SupportsPow3[_E, _M, _T_co], exp: _E, mod: _M) -> _T_co: ...
    @overload
    def pow(base: _SupportsSomeKindOfPow, exp: float, mod: None = None) -> Any: ...
    @overload
    def pow(base: _SupportsSomeKindOfPow, exp: complex, mod: None = None) -> complex: ...

else:
    @overload
    def pow(__x: int, __y: int, __z: int) -> int: ...
    @overload
    def pow(__x: int, __y: Literal[0], __z: None = None) -> Literal[1]: ...
    @overload
    def pow(__x: int, __y: _PositiveInteger, __z: None = None) -> int: ...
    @overload
    def pow(__x: int, __y: _NegativeInteger, __z: None = None) -> float: ...
    @overload
    def pow(__x: int, __y: int, __z: None = None) -> Any: ...
    @overload
    def pow(__x: _PositiveInteger, __y: float, __z: None = None) -> float: ...
    @overload
    def pow(__x: _NegativeInteger, __y: float, __z: None = None) -> complex: ...
    @overload
    def pow(__x: float, __y: int, __z: None = None) -> float: ...
    @overload
    def pow(__x: float, __y: complex | _SupportsSomeKindOfPow, __z: None = None) -> Any: ...
    @overload
    def pow(__x: complex, __y: complex | _SupportsSomeKindOfPow, __z: None = None) -> complex: ...
    @overload
    def pow(__x: _SupportsPow2[_E, _T_co], __y: _E, __z: None = None) -> _T_co: ...
    @overload
    def pow(__x: _SupportsPow3NoneOnly[_E, _T_co], __y: _E, __z: None = None) -> _T_co: ...
    @overload
    def pow(__x: _SupportsPow3[_E, _M, _T_co], __y: _E, __z: _M) -> _T_co: ...
    @overload
    def pow(__x: _SupportsSomeKindOfPow, __y: float, __z: None = None) -> Any: ...
    @overload
    def pow(__x: _SupportsSomeKindOfPow, __y: complex, __z: None = None) -> complex: ...

def quit(code: sys._ExitCode = None) -> NoReturn: ...

class reversed(Iterator[_T], Generic[_T]):
    @overload
    def __init__(self, __sequence: Reversible[_T]) -> None: ...
    @overload
    def __init__(self, __sequence: SupportsLenAndGetItem[_T]) -> None: ...
    def __iter__(self) -> Self: ...
    def __next__(self) -> _T: ...
    def __length_hint__(self) -> int: ...

def repr(__obj: object) -> str: ...

# See https://github.com/python/typeshed/pull/9141
# and https://github.com/python/typeshed/pull/9151
# on why we don't use `SupportsRound` from `typing.pyi`

class _SupportsRound1(Protocol[_T_co]):
    def __round__(self) -> _T_co: ...

class _SupportsRound2(Protocol[_T_co]):
    def __round__(self, __ndigits: int) -> _T_co: ...

@overload
def round(number: _SupportsRound1[_T], ndigits: None = None) -> _T: ...
@overload
def round(number: _SupportsRound2[_T], ndigits: SupportsIndex) -> _T: ...

# See https://github.com/python/typeshed/pull/6292#discussion_r748875189
# for why arg 3 of `setattr` should be annotated with `Any` and not `object`
def setattr(__obj: object, __name: str, __value: Any) -> None: ...
@overload
def sorted(
    __iterable: Iterable[SupportsRichComparisonT], *, key: None = None, reverse: bool = False
) -> list[SupportsRichComparisonT]: ...
@overload
def sorted(__iterable: Iterable[_T], *, key: Callable[[_T], SupportsRichComparison], reverse: bool = False) -> list[_T]: ...

_AddableT1 = TypeVar("_AddableT1", bound=SupportsAdd[Any, Any])
_AddableT2 = TypeVar("_AddableT2", bound=SupportsAdd[Any, Any])

class _SupportsSumWithNoDefaultGiven(SupportsAdd[Any, Any], SupportsRAdd[int, Any], Protocol): ...

_SupportsSumNoDefaultT = TypeVar("_SupportsSumNoDefaultT", bound=_SupportsSumWithNoDefaultGiven)

# In general, the return type of `x + x` is *not* guaranteed to be the same type as x.
# However, we can't express that in the stub for `sum()`
# without creating many false-positive errors (see #7578).
# Instead, we special-case the most common examples of this: bool and literal integers.
if sys.version_info >= (3, 8):
    @overload
    def sum(__iterable: Iterable[bool], start: int = 0) -> int: ...  # type: ignore[misc]

else:
    @overload
    def sum(__iterable: Iterable[bool], __start: int = 0) -> int: ...  # type: ignore[misc]

@overload
def sum(__iterable: Iterable[_SupportsSumNoDefaultT]) -> _SupportsSumNoDefaultT | Literal[0]: ...

if sys.version_info >= (3, 8):
    @overload
    def sum(__iterable: Iterable[_AddableT1], start: _AddableT2) -> _AddableT1 | _AddableT2: ...

else:
    @overload
    def sum(__iterable: Iterable[_AddableT1], __start: _AddableT2) -> _AddableT1 | _AddableT2: ...

# The argument to `vars()` has to have a `__dict__` attribute, so the second overload can't be annotated with `object`
# (A "SupportsDunderDict" protocol doesn't work)
# Use a type: ignore to make complaints about overlapping overloads go away
@overload
def vars(__object: type) -> types.MappingProxyType[str, Any]: ...  # type: ignore[misc]
@overload
def vars(__object: Any = ...) -> dict[str, Any]: ...

class zip(Iterator[_T_co], Generic[_T_co]):
    if sys.version_info >= (3, 10):
        @overload
        def __new__(cls, __iter1: Iterable[_T1], *, strict: bool = ...) -> zip[tuple[_T1]]: ...
        @overload
        def __new__(cls, __iter1: Iterable[_T1], __iter2: Iterable[_T2], *, strict: bool = ...) -> zip[tuple[_T1, _T2]]: ...
        @overload
        def __new__(
            cls, __iter1: Iterable[_T1], __iter2: Iterable[_T2], __iter3: Iterable[_T3], *, strict: bool = ...
        ) -> zip[tuple[_T1, _T2, _T3]]: ...
        @overload
        def __new__(
            cls,
            __iter1: Iterable[_T1],
            __iter2: Iterable[_T2],
            __iter3: Iterable[_T3],
            __iter4: Iterable[_T4],
            *,
            strict: bool = ...,
        ) -> zip[tuple[_T1, _T2, _T3, _T4]]: ...
        @overload
        def __new__(
            cls,
            __iter1: Iterable[_T1],
            __iter2: Iterable[_T2],
            __iter3: Iterable[_T3],
            __iter4: Iterable[_T4],
            __iter5: Iterable[_T5],
            *,
            strict: bool = ...,
        ) -> zip[tuple[_T1, _T2, _T3, _T4, _T5]]: ...
        @overload
        def __new__(
            cls,
            __iter1: Iterable[Any],
            __iter2: Iterable[Any],
            __iter3: Iterable[Any],
            __iter4: Iterable[Any],
            __iter5: Iterable[Any],
            __iter6: Iterable[Any],
            *iterables: Iterable[Any],
            strict: bool = ...,
        ) -> zip[tuple[Any, ...]]: ...
    else:
        @overload
        def __new__(cls, __iter1: Iterable[_T1]) -> zip[tuple[_T1]]: ...
        @overload
        def __new__(cls, __iter1: Iterable[_T1], __iter2: Iterable[_T2]) -> zip[tuple[_T1, _T2]]: ...
        @overload
        def __new__(cls, __iter1: Iterable[_T1], __iter2: Iterable[_T2], __iter3: Iterable[_T3]) -> zip[tuple[_T1, _T2, _T3]]: ...
        @overload
        def __new__(
            cls, __iter1: Iterable[_T1], __iter2: Iterable[_T2], __iter3: Iterable[_T3], __iter4: Iterable[_T4]
        ) -> zip[tuple[_T1, _T2, _T3, _T4]]: ...
        @overload
        def __new__(
            cls,
            __iter1: Iterable[_T1],
            __iter2: Iterable[_T2],
            __iter3: Iterable[_T3],
            __iter4: Iterable[_T4],
            __iter5: Iterable[_T5],
        ) -> zip[tuple[_T1, _T2, _T3, _T4, _T5]]: ...
        @overload
        def __new__(
            cls,
            __iter1: Iterable[Any],
            __iter2: Iterable[Any],
            __iter3: Iterable[Any],
            __iter4: Iterable[Any],
            __iter5: Iterable[Any],
            __iter6: Iterable[Any],
            *iterables: Iterable[Any],
        ) -> zip[tuple[Any, ...]]: ...

    def __iter__(self) -> Self: ...
    def __next__(self) -> _T_co: ...

# Signature of `builtins.__import__` should be kept identical to `importlib.__import__`
# Return type of `__import__` should be kept the same as return type of `importlib.import_module`
def __import__(
    name: str,
    globals: Mapping[str, object] | None = None,
    locals: Mapping[str, object] | None = None,
    fromlist: Sequence[str] = (),
    level: int = 0,
) -> types.ModuleType: ...
def __build_class__(__func: Callable[[], _Cell | Any], __name: str, *bases: Any, metaclass: Any = ..., **kwds: Any) -> Any: ...

# Actually the type of Ellipsis is <type 'ellipsis'>, but since it's
# not exposed anywhere under that name, we make it private here.
@final
@type_check_only
class ellipsis: ...

Ellipsis: ellipsis

class BaseException:
    args: tuple[Any, ...]
    __cause__: BaseException | None
    __context__: BaseException | None
    __suppress_context__: bool
    __traceback__: TracebackType | None
    def __init__(self, *args: object) -> None: ...
    def __setstate__(self, __state: dict[str, Any] | None) -> None: ...
    def with_traceback(self, __tb: TracebackType | None) -> Self: ...
    if sys.version_info >= (3, 11):
        # only present after add_note() is called
        __notes__: list[str]
        def add_note(self, __note: str) -> None: ...

class GeneratorExit(BaseException): ...
class KeyboardInterrupt(BaseException): ...

class SystemExit(BaseException):
    code: sys._ExitCode

class Exception(BaseException): ...

class StopIteration(Exception):
    value: Any

class OSError(Exception):
    errno: int
    strerror: str
    # filename, filename2 are actually str | bytes | None
    filename: Any
    filename2: Any
    if sys.platform == "win32":
        winerror: int

EnvironmentError = OSError
IOError = OSError
if sys.platform == "win32":
    WindowsError = OSError

class ArithmeticError(Exception): ...
class AssertionError(Exception): ...

class AttributeError(Exception):
    if sys.version_info >= (3, 10):
        def __init__(self, *args: object, name: str | None = ..., obj: object = ...) -> None: ...
        name: str
        obj: object

class BufferError(Exception): ...
class EOFError(Exception): ...

class ImportError(Exception):
    def __init__(self, *args: object, name: str | None = ..., path: str | None = ...) -> None: ...
    name: str | None
    path: str | None
    msg: str  # undocumented
    if sys.version_info >= (3, 12):
        name_from: str | None  # undocumented

class LookupError(Exception): ...
class MemoryError(Exception): ...

class NameError(Exception):
    if sys.version_info >= (3, 10):
        name: str

class ReferenceError(Exception): ...
class RuntimeError(Exception): ...

class StopAsyncIteration(Exception):
    value: Any

class SyntaxError(Exception):
    msg: str
    lineno: int | None
    offset: int | None
    text: str | None
    filename: str | None
    if sys.version_info >= (3, 10):
        end_lineno: int | None
        end_offset: int | None

class SystemError(Exception): ...
class TypeError(Exception): ...
class ValueError(Exception): ...
class FloatingPointError(ArithmeticError): ...
class OverflowError(ArithmeticError): ...
class ZeroDivisionError(ArithmeticError): ...
class ModuleNotFoundError(ImportError): ...
class IndexError(LookupError): ...
class KeyError(LookupError): ...
class UnboundLocalError(NameError): ...

class BlockingIOError(OSError):
    characters_written: int

class ChildProcessError(OSError): ...
class ConnectionError(OSError): ...
class BrokenPipeError(ConnectionError): ...
class ConnectionAbortedError(ConnectionError): ...
class ConnectionRefusedError(ConnectionError): ...
class ConnectionResetError(ConnectionError): ...
class FileExistsError(OSError): ...
class FileNotFoundError(OSError): ...
class InterruptedError(OSError): ...
class IsADirectoryError(OSError): ...
class NotADirectoryError(OSError): ...
class PermissionError(OSError): ...
class ProcessLookupError(OSError): ...
class TimeoutError(OSError): ...
class NotImplementedError(RuntimeError): ...
class RecursionError(RuntimeError): ...
class IndentationError(SyntaxError): ...
class TabError(IndentationError): ...
class UnicodeError(ValueError): ...

class UnicodeDecodeError(UnicodeError):
    encoding: str
    object: bytes
    start: int
    end: int
    reason: str
    def __init__(self, __encoding: str, __object: ReadableBuffer, __start: int, __end: int, __reason: str) -> None: ...

class UnicodeEncodeError(UnicodeError):
    encoding: str
    object: str
    start: int
    end: int
    reason: str
    def __init__(self, __encoding: str, __object: str, __start: int, __end: int, __reason: str) -> None: ...

class UnicodeTranslateError(UnicodeError):
    encoding: None
    object: str
    start: int
    end: int
    reason: str
    def __init__(self, __object: str, __start: int, __end: int, __reason: str) -> None: ...

class Warning(Exception): ...
class UserWarning(Warning): ...
class DeprecationWarning(Warning): ...
class SyntaxWarning(Warning): ...
class RuntimeWarning(Warning): ...
class FutureWarning(Warning): ...
class PendingDeprecationWarning(Warning): ...
class ImportWarning(Warning): ...
class UnicodeWarning(Warning): ...
class BytesWarning(Warning): ...
class ResourceWarning(Warning): ...

if sys.version_info >= (3, 10):
    class EncodingWarning(Warning): ...

if sys.version_info >= (3, 11):
    _BaseExceptionT_co = TypeVar("_BaseExceptionT_co", bound=BaseException, covariant=True)
    _BaseExceptionT = TypeVar("_BaseExceptionT", bound=BaseException)
    _ExceptionT_co = TypeVar("_ExceptionT_co", bound=Exception, covariant=True)
    _ExceptionT = TypeVar("_ExceptionT", bound=Exception)

    # See `check_exception_group.py` for use-cases and comments.
    class BaseExceptionGroup(BaseException, Generic[_BaseExceptionT_co]):
        def __new__(cls, __message: str, __exceptions: Sequence[_BaseExceptionT_co]) -> Self: ...
        def __init__(self, __message: str, __exceptions: Sequence[_BaseExceptionT_co]) -> None: ...
        @property
        def message(self) -> str: ...
        @property
        def exceptions(self) -> tuple[_BaseExceptionT_co | BaseExceptionGroup[_BaseExceptionT_co], ...]: ...
        @overload
        def subgroup(
            self, __condition: type[_ExceptionT] | tuple[type[_ExceptionT], ...]
        ) -> ExceptionGroup[_ExceptionT] | None: ...
        @overload
        def subgroup(
            self, __condition: type[_BaseExceptionT] | tuple[type[_BaseExceptionT], ...]
        ) -> BaseExceptionGroup[_BaseExceptionT] | None: ...
        @overload
        def subgroup(
            self, __condition: Callable[[_BaseExceptionT_co | Self], bool]
        ) -> BaseExceptionGroup[_BaseExceptionT_co] | None: ...
        @overload
        def split(
            self, __condition: type[_ExceptionT] | tuple[type[_ExceptionT], ...]
        ) -> tuple[ExceptionGroup[_ExceptionT] | None, BaseExceptionGroup[_BaseExceptionT_co] | None]: ...
        @overload
        def split(
            self, __condition: type[_BaseExceptionT] | tuple[type[_BaseExceptionT], ...]
        ) -> tuple[BaseExceptionGroup[_BaseExceptionT] | None, BaseExceptionGroup[_BaseExceptionT_co] | None]: ...
        @overload
        def split(
            self, __condition: Callable[[_BaseExceptionT_co | Self], bool]
        ) -> tuple[BaseExceptionGroup[_BaseExceptionT_co] | None, BaseExceptionGroup[_BaseExceptionT_co] | None]: ...
        # In reality it is `NonEmptySequence`:
        @overload
        def derive(self, __excs: Sequence[_ExceptionT]) -> ExceptionGroup[_ExceptionT]: ...
        @overload
        def derive(self, __excs: Sequence[_BaseExceptionT]) -> BaseExceptionGroup[_BaseExceptionT]: ...
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...

    class ExceptionGroup(BaseExceptionGroup[_ExceptionT_co], Exception):
        def __new__(cls, __message: str, __exceptions: Sequence[_ExceptionT_co]) -> Self: ...
        def __init__(self, __message: str, __exceptions: Sequence[_ExceptionT_co]) -> None: ...
        @property
        def exceptions(self) -> tuple[_ExceptionT_co | ExceptionGroup[_ExceptionT_co], ...]: ...
        # We accept a narrower type, but that's OK.
        @overload  # type: ignore[override]
        def subgroup(
            self, __condition: type[_ExceptionT] | tuple[type[_ExceptionT], ...]
        ) -> ExceptionGroup[_ExceptionT] | None: ...
        @overload
        def subgroup(self, __condition: Callable[[_ExceptionT_co | Self], bool]) -> ExceptionGroup[_ExceptionT_co] | None: ...
        @overload  # type: ignore[override]
        def split(
            self, __condition: type[_ExceptionT] | tuple[type[_ExceptionT], ...]
        ) -> tuple[ExceptionGroup[_ExceptionT] | None, ExceptionGroup[_ExceptionT_co] | None]: ...
        @overload
        def split(
            self, __condition: Callable[[_ExceptionT_co | Self], bool]
        ) -> tuple[ExceptionGroup[_ExceptionT_co] | None, ExceptionGroup[_ExceptionT_co] | None]: ...
