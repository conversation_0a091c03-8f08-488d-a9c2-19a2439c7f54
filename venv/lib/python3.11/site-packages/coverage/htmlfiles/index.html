{# Licensed under the Apache License: http://www.apache.org/licenses/LICENSE-2.0 #}
{# For details: https://github.com/nedbat/coveragepy/blob/master/NOTICE.txt #}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>{{ title|escape }}</title>
    <link rel="icon" sizes="32x32" href="{{ statics.favicon_32_png }}">
    <link rel="stylesheet" href="{{ statics.style_css }}" type="text/css">
    {% if extra_css %}
        <link rel="stylesheet" href="{{ statics.extra_css }}" type="text/css">
    {% endif %}
    <script src="{{ statics.coverage_html_js }}" defer></script>
</head>
<body class="indexfile">

<header>
    <div class="content">
        <h1>{{ title|escape }}:
            <span class="pc_cov">{{totals.pc_covered_str}}%</span>
        </h1>

        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="{{ statics.keybd_closed_png }}" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        {% if region_noun %}
                        <kbd>n</kbd>
                        {% endif %}
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        {% if has_arcs %}
                        <kbd>b</kbd>
                        <kbd>p</kbd>
                        {% endif %}
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>

        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" {% if skip_covered %}checked disabled {% endif %}>
                <label for="hide100">hide covered</label>
            </div>
        </form>

        <h2>
            {% for ibtn in index_buttons %}
                <a class="button{% if ibtn.current %} current{% endif %}"{% if ibtn.url %} href="{{ ibtn.url }}"{% endif %}>{{ ibtn.label }}</a>{#-#}
            {% endfor %}
        </h2>

        <p class="text">
            <a class="nav" href="{{__url__}}">coverage.py v{{__version__}}</a>,
            created at {{ time_stamp }}
        </p>
    </div>
</header>

<main id="index">
    <table class="index" data-sortable>
        <thead>
            {# The title="" attr doesn't work in Safari. #}
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                {% if region_noun %}
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">{{ region_noun }}<span class="arrows"></span></th>
                {% endif %}
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                {% if has_arcs %}
                <th id="branches" aria-sort="none" data-default-sort-order="descending" data-shortcut="b">branches<span class="arrows"></span></th>
                <th id="partial" aria-sort="none" data-default-sort-order="descending" data-shortcut="p">partial<span class="arrows"></span></th>
                {% endif %}
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            {% for region in regions %}
            <tr class="region">
                <td class="name left"><a href="{{region.url}}">{{region.file}}</a></td>
                {% if region_noun %}
                <td class="name left"><a href="{{region.url}}">{{region.description}}</a></td>
                {% endif %}
                <td>{{region.nums.n_statements}}</td>
                <td>{{region.nums.n_missing}}</td>
                <td>{{region.nums.n_excluded}}</td>
                {% if has_arcs %}
                <td>{{region.nums.n_branches}}</td>
                <td>{{region.nums.n_partial_branches}}</td>
                {% endif %}
                <td class="right" data-ratio="{{region.nums.ratio_covered|pair}}">{{region.nums.pc_covered_str}}%</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                {% if region_noun %}
                <td class="name left">&nbsp;</td>
                {% endif %}
                <td>{{totals.n_statements}}</td>
                <td>{{totals.n_missing}}</td>
                <td>{{totals.n_excluded}}</td>
                {% if has_arcs %}
                <td>{{totals.n_branches}}</td>
                <td>{{totals.n_partial_branches}}</td>
                {% endif %}
                <td class="right" data-ratio="{{totals.ratio_covered|pair}}">{{totals.pc_covered_str}}%</td>
            </tr>
        </tfoot>
    </table>

    <p id="no_rows">
        No items found using the specified filter.
    </p>

    {% if skipped_covered_msg %}
        <p>{{ skipped_covered_msg }}</p>
    {% endif %}
    {% if skipped_empty_msg %}
        <p>{{ skipped_empty_msg }}</p>
    {% endif %}
</main>

<footer>
    <div class="content">
        <p>
            <a class="nav" href="{{__url__}}">coverage.py v{{__version__}}</a>,
            created at {{ time_stamp }}
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="{{ final_html }}"></a>
        <a id="nextFileLink" class="nav" href="{{ first_html }}"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>

</body>
</html>
